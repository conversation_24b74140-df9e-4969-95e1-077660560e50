<?php

namespace App\Services\Payment;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;

interface PaymentServiceInterface
{
    public function addMethode(Request $request);
    public function updateMethode(Request $request);
    public function getMethode(int $id);
    public function changeStatus(Request $request,);
    public function getAllMethode();
    public function getActiveMethode();
    public function removeMethode(Request $request,);
}
