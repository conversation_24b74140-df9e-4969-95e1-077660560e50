<?php

namespace App\Controller;

use App\Services\Printer\PrinterServiceInterface;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * @Rest\Route("/printer" , name="printer_" )
 */
class PrinterApiController extends AbstractFOSRestController
{

    private PrinterServiceInterface $printerService;
    public function __construct(PrinterServiceInterface $printerService)
    {
        $this->printerService = $printerService;
    }

    /**
     * @Rest\Post("/" , name="create" )
     */
    public function addPrinter(Request $request)
    {
        $res = $this->printerService->add($request);
        if ($res['status'] === Response::HTTP_CREATED) {
            $view = $this->view($res["data"], Response::HTTP_CREATED, []);
        } else {
            $view = $this->view($res["data"], $res['status'], []);
        }
        return $this->handleView($view);
    }

    /**
     * @Rest\Post("/{id}" , name="update" )
     */
    public function update(Request $request)
    {
        $res = $this->printerService->update($request);
        if ($res['status'] === Response::HTTP_CREATED) {
            $view = $this->view($res["data"], Response::HTTP_CREATED, []);
        } else {
            $view = $this->view($res["data"], $res['status'], []);
        }
        return $this->handleView($view);
    }

    /**
     * @Rest\Get("s" , name="get_all")
     */
    public function getAll()
    {
        $res = $this->printerService->getAll();
        if ($res['status'] === Response::HTTP_OK) {
            $view = $this->view($res["data"], Response::HTTP_OK, []);
        } else {
            $view = $this->view($res["data"], $res['status'], []);
        }
        return $this->handleView($view);
    }

    /**
     * @Rest\Post("/delete/{id}" , name="delete")
     */
    public function deletePrinter(int $id)
    {
        $res = $this->printerService->delete($id);
        if ($res['status'] === Response::HTTP_OK) {
            $view = $this->view($res["data"], Response::HTTP_OK, []);
        } else {
            $view = $this->view($res["data"], $res['status'], []);
        }
        return $this->handleView($view);
    }

    /**
     * @Rest\Post("/do_action" , name="do_action")
     */
    public function doAction(Request $request)
    {
        $res = $this->printerService->doAction($request);
        if ($res['status'] === Response::HTTP_OK) {
            $view = $this->view($res["data"], Response::HTTP_OK, []);
        } else {
            $view = $this->view($res["data"], $res['status'], []);
        }
        return $this->handleView($view);
    }
}
