<?php

namespace App\Command;

use App\Entity\Category;
use App\Entity\Options;
use App\Entity\OptionValues;
use App\Entity\Products;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class SyncDatabaseCommand extends Command
{
    protected static $defaultName = 'sync:db';
    private $em;
    private $httpClient;

    public function __construct(EntityManagerInterface $em, HttpClientInterface $httpClient)
    {
        $this->em = $em;
        $this->httpClient = $httpClient;
        parent::__construct();
    }
    protected function configure(): void
    {
        // ...-up
    }

    /**
     * @throws \Exception
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $response = $this->httpClient->request('GET', 'http://192.168.1.134:8069/api/v1/products', [
            'headers' => [
                'api-key' => '9134d632-aec6-4719-b873-9b2ecaeb43d0',
            ],
        ]);
        if ($response->getStatusCode() !== 200) {
            throw new \Exception($response->getContent());
        }
        $jsonResponse = json_decode($response->getContent(), true);
        $products = $jsonResponse['records'];

        foreach ($products as $odooProduct) {
            $product = $this->em->getRepository(Products::class)->findOneBy(['externalId' => $odooProduct['id']]);
            if (!$product) {
                $product = new Products();
                $product->setName($odooProduct['name']);
                $product->setDescription($odooProduct['description']);
                $product->setCreatedAt(new DateTime());
                $product->setModifiedAt(new DateTime());
                $product->setPrice(10);
                $product->setCost(0);
                $product->setTax(0);
                $product->setDiscount(0);
                $product->setStatus('published'); // TODO: Change to get status from odoo
                $product->setSUK('200005'); // TODO: Change to get SUK from odoo or delete it if not needed 
                $product->setPreparationTime(1); // TODO: Change to get preperation time from odoo or delete it if not needed 
                $product->setUnit('unit'); // TODO: Change to get unit from odoo or delete it if not needed
                $product->setUnitValue('unitValue'); // TODO: Change to get unit value from odoo or delete it if not needed
                $product->setAttributes([
                    'weight' => 'weight',
                ]); // TODO: Change to get attributes from odoo or delete it if not needed
                $product->setIngredients([
                    'weight' => 'weight',
                ]); // TODO: Change to get ingredients from odoo or delete it if not needed
                $product->setIsFeatured($odooProduct['is_featured']);

                // TODO: Add Tags from odoo or delete it if not needed
                /* if ($request->get('tag') != 'undefined') {
                    $tagsId = json_decode($request->get('tag'), true);
                    if (is_array($tagsId)) {
                        foreach ($tagsId as $tagId) {
                            $tag = $this->em->getRepository(Tags::class)->find($tagId);
                            $product->addTag($tag);
                        }
                    } else {
                        $tag = $this->em->getRepository(Tags::class)->find($request->get('tag'));
                        $product->addTag($tag);
                    }
                } */
                //TODO: add stock and stock threshold from odoo or delete it if not needed
                $product->setStockable($odooProduct['is_stockable']);
                $product->setIsAddon($odooProduct['is_addon']);
                $product->setIsPack($odooProduct['is_pack']);

                if ($odooProduct['pos_categ_id'] === false) {
                    $category = $this->em->getRepository(Category::class)->findOneBy(['name' => 'undefined']);
                } else {
                    $category = $this->em->getRepository(Category::class)->findOneBy(['externalId' => $odooProduct['pos_categ_id'][0]]);
                    if (!$category) {
                        $category = new Category();
                        $category->setName($odooProduct['pos_categ_id'][1]);
                        $category->setExternalId($odooProduct['pos_categ_id'][0]);
                        $category->setImage('/images/category.png'); //TODO: Change to get image from odoo
                        $category->setCreatedBy('sync');
                        $category->setModifedBy('sync');
                        $this->em->persist($category);
                        $this->em->flush();
                    }
                }
                $product->setCategory($category);
                /* $image = $request->files->get('image');
                $imageLink = Uploader::uploadImageToWebp($image, 'products'); */
                //TODO: Change to get image from odoo
                $product->setIsCustomizable($odooProduct['is_customizable']);
                $product->setImage('/images/products/pack-product-1-6726a3f66d2177.61320282.webp');
                $product->setCreatedBy('sync');
                $product->setModifedBy('sync');
                $product->setExternalId($odooProduct['id']);
                $this->em->persist($product);
                $this->em->flush();

                if ($odooProduct['is_customizable']) {
                    $options = $odooProduct['variants'];
                    foreach ($options as $option) {
                        $step = new Options();
                        $step->setExternalId($option['id']);
                        $step->setName($option['name']);
                        $step->setIsRequired($option['is_required']);
                        $step->setIsMultiple($option['is_multiple']);
                        if (array_key_exists('has_quantity', $option)) {
                            $step->setHasQuantity($option['hasQuantity']);
                        }
                        foreach ($option['variantsValues'] as $value) {
                            $stepValue = $this->em->getRepository(OptionValues::class)->findOneBy(['externalId' => $value['id']]);
                            if (!$stepValue) {
                                $stepValue = new OptionValues();
                                $stepValue->setName($value['name']);
                                $stepValue->setPrice($value['price']);
                                $stepValue->setExternalId($value['id']);
                                $stepValue->setImage('/images/optionValue.png');
                                $this->em->persist($stepValue);
                                $this->em->flush();
                            }
                            $step->addOptionValue($stepValue);
                        }
                        if ($option['default_product_variants_values_id']) {
                            //check if it is an array 
                            $stepDefaultValue = $this->em->getRepository(OptionValues::class)->findOneBy(['externalId' => $option['default_product_variants_values_id'][0]]);
                            $step->addDefaultOptionsValue($stepDefaultValue);
                        }
                        $product->addOption($step);
                        $this->em->persist($step);
                    }
                }

                $this->em->flush();
            } else {
                $output->writeln('Product with name ' . $odooProduct['name'] . ' already exists.');
                if ($product->getName() != $odooProduct['name']) $product->setName($odooProduct['name']);
                if ($product->getDescription() != $odooProduct['description']) $product->setDescription($odooProduct['description']);
                $product->setModifiedAt(new DateTime());
                if ($product->getPrice() != 10) $product->setPrice(10);
                if ($product->getCost() != 0) $product->setCost(0);
                if ($product->getTax() != 0) $product->setTax(0);
                if ($product->getDiscount() != 0) $product->setDiscount(0);
                if ($product->getStatus() != 'published') $product->setStatus('published'); // TODO: Change to get status from odoo
                if ($product->getSUK() != '200005') $product->setSUK('200005'); // TODO: Change to get SUK from odoo or delete it if not needed 
                if ($product->getPreparationTime() != 1) $product->setPreparationTime(1); // TODO: Change to get preperation time from odoo or delete it if not needed 
                if ($product->getUnit() != 'unit') $product->setUnit('unit'); // TODO: Change to get unit from odoo or delete it if not needed
                if ($product->getUnitValue() != 'unitValue') $product->setUnitValue('unitValue'); // TODO: Change to get unit value from odoo or delete it if not needed
                if ($product->getAttributes() != ['weight' => 'weight']) $product->setAttributes([
                    'weight' => 'weight',
                ]); // TODO: Change to get attributes from odoo or delete it if not needed
                if ($product->getIngredients() != ['weight' => 'weight']) $product->setIngredients([
                    'weight' => 'weight',
                ]); // TODO: Change to get ingredients from odoo or delete it if not needed
                if ($product->isIsFeatured() != $odooProduct['is_featured']) $product->setIsFeatured($odooProduct['is_featured']);

                // TODO: Add Tags from odoo or delete it if not needed
                /* if ($request->get('tag') != 'undefined') {
                    $tagsId = json_decode($request->get('tag'), true);
                    if (is_array($tagsId)) {
                        foreach ($tagsId as $tagId) {
                            $tag = $this->em->getRepository(Tags::class)->find($tagId);
                            $product->addTag($tag);
                        }
                    } else {
                        $tag = $this->em->getRepository(Tags::class)->find($request->get('tag'));
                        $product->addTag($tag);
                    }
                } */
                //TODO: add stock and stock threshold from odoo or delete it if not needed
                if ($product->isStockable() != $odooProduct['is_stockable']) $product->setStockable($odooProduct['is_stockable']);
                if ($product->isIsAddon() != $odooProduct['is_addon']) $product->setIsAddon($odooProduct['is_addon']);
                if ($product->isIsPack() != $odooProduct['is_pack']) $product->setIsPack($odooProduct['is_pack']);

                if ($odooProduct['pos_categ_id'] === false) {
                    $category = $this->em->getRepository(Category::class)->findOneBy(['name' => 'undefined']);
                } else {
                    $category = $this->em->getRepository(Category::class)->findOneBy(['externalId' => $odooProduct['pos_categ_id'][0]]);
                    if (!$category) {
                        $category = new Category();
                        $category->setName($odooProduct['pos_categ_id'][1]);
                        $category->setImage('/images/category.png'); //TODO: Change to get image from odoo
                        $category->setCreatedBy('sync');
                        $category->setModifedBy('sync');
                        $category->setExternalId($odooProduct['pos_categ_id'][0]);
                        $this->em->persist($category);
                        $this->em->flush();
                    }
                }
                $product->setCategory($category);

                /* $image = $request->files->get('image');
                $imageLink = Uploader::uploadImageToWebp($image, 'products'); */
                //TODO: Change to get image from odoo
                if ($product->isIsCustomizable() != $odooProduct['is_customizable']) $product->setIsCustomizable($odooProduct['is_customizable']);
                if ($product->getImage() != '/images/products/pack-product-1-6726a3f66d2177.61320282.webp') $product->setImage('/images/products/pack-product-1-6726a3f66d2177.61320282.webp');

                $product->setModifedBy('sync');
                $this->em->flush();

                if ($odooProduct['is_customizable']) {
                    $options = $odooProduct['variants'];
                    foreach ($product->getOptions() as $option) {
                        $product->removeOption($option);
                    }
                    foreach ($options as $option) {
                        $step = $this->em->getRepository(Options::class)->findOneBy(['externalId' => $option['id']]);
                        if (!$step) {
                            $step = new Options();
                            $step->setExternalId($option['id']);
                        }
                        $step->setName($option['name']);
                        $step->setIsRequired($option['is_required']);
                        $step->setIsMultiple($option['is_multiple']);
                        if (array_key_exists('has_quantity', $option)) {
                            $step->setHasQuantity($option['hasQuantity']);
                        }
                        foreach ($option['variantsValues'] as $value) {
                            $stepValue = $this->em->getRepository(OptionValues::class)->findOneBy(['externalId' => $value['id']]);
                            if (!$stepValue) {
                                $stepValue = new OptionValues();
                                $stepValue->setName($value['name']);
                                $stepValue->setPrice($value['price']);
                                $stepValue->setExternalId($value['id']);
                                $stepValue->setImage('/images/optionValue.png');
                                $this->em->persist($stepValue);
                                $this->em->flush();
                            } else {
                                if ($stepValue->getName() != $value['name']) $stepValue->setName($value['name']);
                                if ($stepValue->getPrice() != $value['price']) $stepValue->setPrice($value['price']);
                                if ($stepValue->getImage() != '/images/optionValue.png') $stepValue->setImage('/images/optionValue.png');
                            }
                            $step->addOptionValue($stepValue);
                        }
                        if ($option['default_product_variants_values_id']) {
                            //check if it is an array 
                            $stepDefaultValue = $this->em->getRepository(OptionValues::class)->findOneBy(['externalId' => $option['default_product_variants_values_id'][0]]);
                            $step->addDefaultOptionsValue($stepDefaultValue);
                        } else {
                            foreach ($step->getDefaultOptionsValues() as $value) {
                                $step->removeDefaultOptionsValue($value);
                            }
                        }
                        $product->addOption($step);
                        $this->em->persist($step);
                    }
                }
                $this->em->flush();
            }
        }

        $output->writeln('Synchronization completed.');


        return Command::SUCCESS;
    }
}
