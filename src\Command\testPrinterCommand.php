<?php

namespace App\Command;

use App\Entity\Category;
use App\Entity\Options;
use App\Entity\OptionValues;
use App\Entity\Products;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Mike42\Escpos\PrintConnectors\NetworkPrintConnector;
use Mike42\Escpos\Printer;

class testPrinterCommand extends Command
{
    protected static $defaultName = 'printer:test';
    private $em;
    private $httpClient;

    public function __construct(EntityManagerInterface $em, HttpClientInterface $httpClient)
    {
        $this->em = $em;
        $this->httpClient = $httpClient;
        parent::__construct();
    }
    protected function configure(): void
    {
        // ...-up
    }

    /**
     * @throws \Exception
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $connector = new NetworkPrintConnector("192.168.1.111", 9100);
        $printer = new Printer($connector);
        $printer->text("Hello World!");
        $printer->text("Hello World!");
        $printer->text("Hello World!");
        $printer->text("Hello World!");
        $printer->text("Hello World!");
        $printer->text("Hello World!");
        $printer->text("Hello World!");
        $printer->text("Hello World!");
        $printer->text("Hello World!");
        $printer->text("Hello World!");
        $printer->text("Hello World!");
        $printer->text("Hello World!");
        $printer->text("Hello World!");
        $printer->text("Hello World!");
        $printer->text("Hello World!");
        $printer->text("Hello World!");
        $printer->text("Hello World!");
        $printer->text("Hello World!");
        $printer->text("Hello World!");
        $printer->text("Hello World!");
        $printer->text("Hello World!");
        $printer->text("Hello World!");
        $printer->text("Hello World!\n");
        $printer->cut();
        $printer->close();
        return Command::SUCCESS;
    }
}
