<?php

namespace App\Services\User;

use App\Entity\User;
use Doctrine\ORM\EntityManagerInterface;
use App\Services\User\UserServiceInterface;
use DateTime;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Security\Core\Security;
use Symfony\Component\HttpFoundation\Response;

class UserService implements UserServiceInterface
{
    private $em;
    private $passwordHasher;
    private $security;

    public function __construct(
        EntityManagerInterface $entityManager,
        UserPasswordHasherInterface $passwordHasher,
        Security $security,
    ) {
        $this->em = $entityManager;
        $this->passwordHasher = $passwordHasher;
        $this->security = $security;
    }

    public function allSubUsers()
    {
        $user = $this->em->getRepository(User::class)->findOneBy(['username' => $this->security->getUser()->getUserIdentifier()]);
        $subUsers = $this->em->getRepository(User::class)->findBy(['createdBy' => $user]);
        $allSubUsers = [];
        foreach ($subUsers as $user) {
            $allSubUsers[] = $user->toShow();
        }
        return [
            'status' => Response::HTTP_OK,
            'data' => $allSubUsers
        ];
    }

    public function getUserById(int $id)
    {
        $user = $this->em->getRepository(User::class)->find($id);
        if (!$user) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'data' => 'user not found'
            ];
        }
        return [
            'status' => Response::HTTP_OK,
            'data' => $user->toShow()
        ];
    }

    public function getSubUser(int $userId)
    {
        $user = $this->em->getRepository(User::class)->find($userId);
        if (!$user) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'data' => 'user not found'
            ];
        }
        if ($this->security->getUser() === $user) {
            return [
                'status' => Response::HTTP_SEE_OTHER,
                'data' => "YOU SHOULD SEE OTHER PEOPLE MAN!!"
            ];
        }
        if ($user->getCreatedBy() !== $this->security->getUser() && !$this->security->isGranted('ROLE_MASTER')) {
            return [
                'status' => Response::HTTP_FORBIDDEN,
                'data' => 'you are not allowed to see this user'
            ];
        }
        return [
            'status' => Response::HTTP_OK,
            'data' => $user->toShow()
        ];
    }

    function changePassword(Request $req)
    {
        $user = $this->em->getRepository(User::class)->findOneBy(['username' => $this->security->getUser()->getUserIdentifier()]);
        if ($this->passwordHasher->isPasswordValid($user, $req->get('currentPassword'))) {
            $hashedPassword = $this->passwordHasher->hashPassword(
                $user,
                $req->get("newPassword")
            );
            $user->setPassword($hashedPassword);
            $this->em->flush();
            return [
                'status' => Response::HTTP_OK,
                'data' => ['password changed successfully']
            ];
        }
        return [
            'status' => Response::HTTP_BAD_REQUEST,
            'data' => 'current password is not correct'
        ];
    }

    function changeUserPassword(Request $req)
    {
        $changer = $this->em->getRepository(User::class)->findOneBy(['username' => $this->security->getUser()->getUserIdentifier()]);
        $user = $this->em->getRepository(User::class)->find($req->get('userId'));
        if (!$user)
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'data' => 'user not found'
            ];

        if ($user->getCreatedBy() === $changer || $this->security->isGranted('ROLE_MASTER')) {
            $newPassword = $req->get("password");
            $status = $req->get("active");
            if ($newPassword) {
                if (strlen($newPassword) < 8) {
                    return [
                        'status' => Response::HTTP_BAD_REQUEST,
                        'data' => 'password must be at least 8 characters'
                    ];
                }
                $hashedPassword = $this->passwordHasher->hashPassword(
                    $user,
                    $newPassword
                );
                $user->setPassword($hashedPassword);
            }
            if ($status) {
                $user->setIsActive($status === true);
            }

            $this->em->flush();
            return [
                'status' => Response::HTTP_OK,
                'data' => ['user updated successfully']
            ];
        }
        return [
            'status' => Response::HTTP_FORBIDDEN,
            'data' => 'you are not allowed to change this user'
        ];
    }

    public function register(Request $request)
    {
        $user = $this->em->getRepository(User::class)->findOneBy(['username' => $request->get('username')]);
        if ($user) {
            return [
                'status' => Response::HTTP_BAD_REQUEST,
                'data' => 'username already exists'
            ];
        }
        $user = new User();
        $user->setUsername($request->get('username'));
        $user->setPassword(
            $this->passwordHasher->hashPassword(
                $user,
                $request->get('password')
            )
        );
        $user->setCredit(0);
        $user->setCredit(0);
        $user->setCode($this->generateUniqueCode());
        $user->setCreatedAt(new DateTime());
        $user->setRoles([$request->get('role')]);
        $user->setIsActive(true);
        $this->em->persist($user);
        $this->em->flush();
        return [
            'status' => Response::HTTP_CREATED,
            'data' => ["Created successfully"]
        ];
    }

    public function delete(int $id)
    {
        $user = $this->em->getRepository(User::class)->find($id);
        if (!$user) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'data' => 'user not found'
            ];
        }
        if (!$this->security->isGranted('ROLE_ADMIN')) {
            return [
                'status' => Response::HTTP_FORBIDDEN,
                'data' => 'you are not allowed to delete this user'
            ];
        }
        $this->em->remove($user);
        $this->em->flush();
        return [
            'status' => Response::HTTP_OK,
            'data' => ['user deleted successfully']
        ];
    }

    public function addSubUser(Request $request)
    {
        $username = $request->get('username');
        $password = $request->get('password');
        $ReqRole = $request->get('role');

        $user = $this->em->getRepository(User::class)->findOneBy(['username' => $username]);
        if ($user) {
            return [
                'status' => Response::HTTP_BAD_REQUEST,
                'data' => 'username already exists'
            ];
        }
        if ($this->security->isGranted('ROLE_ADD_SUB_COMERCIAL')) {

            if ($ReqRole !== 'ROLE_COMERCIAL' && $ReqRole !== 'ROLE_ADMIN') {
                return [
                    'status' => Response::HTTP_BAD_REQUEST,
                    'data' => 'Please enter a valid role'
                ];
            }
            if (!is_bool($request->get('allowSubUser')))
                return [
                    'status' => Response::HTTP_BAD_REQUEST,
                    'data' => 'Please enter a valid allowSubUser'
                ];
            $allowSubUser = $request->get('allowSubUser');
        }
        $user = new User();
        $user->setUsername($username);
        $user->setPassword($this->passwordHasher->hashPassword($user, $password));
        $user->setCredit(0);
        $user->setCredit(0);
        $user->setCode($this->generateUniqueCode());
        $user->setIsActive(true);
        $user->setCreatedAt(new DateTime());
        $user->setCreatedBy($this->security->getUser());
        switch ($this->security->getUser()->getRoles()[0]) {
            case 'ROLE_MASTER':
                switch ($ReqRole) {
                    case 'ROLE_SELLER':
                        $roles[] = 'ROLE_SELLER';
                        break;
                    case 'ROLE_VALIDATEUR':
                        $roles[] = 'ROLE_VALIDATEUR';
                        break;

                    default:
                        $roles[] = 'ROLE_COMERCIAL';
                        $roles[] = 'ROLE_ADD_SUB_COMERCIAL';
                        break;
                }
                break;
            case 'ROLE_COMERCIAL':
                if ($this->security->isGranted('ROLE_ADD_SUB_COMERCIAL')) {
                    $roles[] = $ReqRole;
                    if ($allowSubUser) {
                        $roles[] = 'ROLE_ADD_SUB_COMERCIAL';
                    }
                } else {
                    $roles[] = 'ROLE_ADMIN';
                }
                break;
            case 'ROLE_ADMIN':
                $roles[] = 'ROLE_SHOP';
                break;
            case 'ROLE_SHOP':
                $roles[] = 'ROLE_PLAYER';
                break;

            default:
                $roles[] = 'ROLE_PLAYER';
                break;
        }
        $user->setRoles($roles);
        $this->em->persist($user);
        $this->em->flush();
        return [
            'status' => Response::HTTP_CREATED,
            'data' => ["username" => $user->toShow()]
        ];
    }

    public function deleteSubUser(Request $request)
    {
        $userId = $request->get('id');
        $user = $this->em->getRepository(User::class)->find($userId);
        if (!$user) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'data' => 'user not found'
            ];
        }
        if ($user->getCreatedBy() !== $this->security->getUser() && !$this->security->isGranted('ROLE_MASTER')) {
            return [
                'status' => Response::HTTP_FORBIDDEN,
                'data' => 'you are not allowed to delete this user'
            ];
        }
        $this->em->remove($user);
        $this->em->flush();
        return [
            'status' => Response::HTTP_OK,
            'data' => ['user deleted successfully']
        ];
    }

    function generateUniqueCode()
    {
        $characters = 'A0BC2D3EF4GH5IJ6KL7MN8P9QRST1UVWXYZ';
        $charactersLength = strlen($characters);
        $code = '';
        do {
            for ($i = 0; $i < 8; $i++) {
                $code .= $characters[rand(0, $charactersLength - 1)];
            }
            $ticket = $this->em->getRepository(User::class)->findOneBy(["code" => $code]);
        } while ($ticket);


        return $code;
    }

    public function getAllUsers(Request $request)
    {

        $currentPage = $request->get('pagination')['current'] ? $request->get('pagination')['current'] : 1;
        $pageSize = $request->get('pagination')['pageSize'] ? $request->get('pagination')['pageSize'] : 10;
        $search = $request->get('search') ? $request->get('search') : null;
        $sort = $request->get('field') && $request->get('order') ? ['field' => $request->get('field'), 'order' => $request->get('order')] : ['field' => 'id', 'order' => 'descend'];
        $filters = $request->get('filters') ? $request->get('filters') : null;
        $dateType = $request->get('dateType') ? $request->get('dateType') : 'createdAt';
        $sum = [];
        $Users = $this->em->getRepository(User::class)->getAll($search, $currentPage, $pageSize, $sort, $filters, $dateType); //$start, $end,
        $allUsersCount = $this->em->getRepository(User::class)->getAllCount($search, $filters, $dateType); //$start, $end,
        $info = [
            'total' => $allUsersCount,
            'current' => $currentPage,
            'pageSize' => $pageSize,
            'pageSizeOptions' => ['10', '25', '50', '100', '500', '1000'],
            'totalPage' => ceil($allUsersCount / $pageSize)
        ];
        return [
            'status' => Response::HTTP_OK,
            'data' => [
                'info' => $info,
                'results' => $Users,
                'sum' => sizeof($sum) > 0 ? $sum[0] : null,
            ],
        ];
    }

    public function getUsernames()
    {
        $users = $this->em->getRepository(User::class)->findAll();
        $usernames = [];
        foreach ($users as $user) {
            $usernames[] = $user->getUsername();
        }
        return [
            'status' => Response::HTTP_OK,
            'data' => $usernames
        ];
    }

    public function update(Request $request, int $id)
    {
        $user = $this->em->getRepository(User::class)->find($id);
        if (!$user) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'data' => 'user not found'
            ];
        }
        $user->setUsername($request->get('username'));
        $user->setPassword($this->passwordHasher->hashPassword($user, $request->get('password')));
        $user->setCredit($request->get('credit'));
        $user->setRoles($request->get('role'));
        $this->em->persist($user);
        $this->em->flush();
        return [
            'status' => Response::HTTP_OK,
            'data' => [$user->toShow()]
        ];
    }
    public function doAction(Request $request)
    {
        $action = $request->get('action');
        switch ($action) {

            case 'addSubUser':
                return $this->addSubUser($request);
            case 'register':
                return $this->register($request);
            case 'changePassword':
                return $this->changePassword($request);
            case 'changeUserPassword':
                return $this->changeUserPassword($request);
            case 'allSubUsers':
                return $this->allSubUsers();
            case 'getSubUser':
                return $this->getSubUser($request->get('userId'));
            case 'getAllUsers':
                return $this->getAllUsers($request);
            case 'deleteSubUser':
                return $this->deleteSubUser($request);
            default:
                return [
                    'status' => Response::HTTP_BAD_REQUEST,
                    'data' => 'action not found'
                ];
        }
    }
}
