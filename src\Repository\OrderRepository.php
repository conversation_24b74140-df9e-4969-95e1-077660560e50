<?php

namespace App\Repository;

use App\Entity\Order;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Query\Parameter;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Order>
 *
 * @method Order|null find($id, $lockMode = null, $lockVersion = null)
 * @method Order|null findOneBy(array $criteria, array $orderBy = null)
 * @method Order[]    findAll()
 * @method Order[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class OrderRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Order::class);
    }

    //    /**
    //     * @return Order[] Returns an array of Order objects
    //     */
    //    public function findByExampleField($value): array
    //    {
    //        return $this->createQueryBuilder('o')
    //            ->andWhere('o.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->orderBy('o.id', 'ASC')
    //            ->setMaxResults(10)
    //            ->getQuery()
    //            ->getResult()
    //        ;
    //    }

    //    public function findOneBySomeField($value): ?Order
    //    {
    //        return $this->createQueryBuilder('o')
    //            ->andWhere('o.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->getQuery()
    //            ->getOneOrNullResult()
    //        ;
    //    }

    public function getAll($start, $end, $search, $currentPage, $pageSize, $sort, $filters, $dateType)
    {
        $qb = $this->createQueryBuilder('o');
        $qb->leftJoin('o.terminal', 't');
        $qb->leftJoin('o.orderItems', 'oi');
        $qb->leftJoin('oi.product', 'p');
        $qb->select("
            o.id,
            o.paymentStatus,
            o.prepareStatus,
            o.total,
            o.type,
            o.modifiedAt,
            o.createdAt,
            t.id as terminal
        ")
            ->groupBy('o.id, o.paymentStatus, o.prepareStatus, o.total, o.type, o.modifiedAt, o.createdAt, t.id');


        $qb
            ->where('o.' . $dateType . ' BETWEEN :start AND :end')
            ->andWhere(
                $qb->expr()->orX(
                    $qb->expr()->like('o.id', ':search'),
                    $qb->expr()->like('o.paymentStatus', ':search'),
                    $qb->expr()->like('o.prepareStatus', ':search'),
                    $qb->expr()->like('o.createdAt', ':search'),
                    $qb->expr()->like('o.modifiedAt', ':search'),
                    $qb->expr()->like('o.total', ':search'),
                    $qb->expr()->like('o.type', ':search'),
                )
            )
            ->setParameters(new ArrayCollection(
                [
                    new Parameter('search', '%' . $search . '%'),
                    new Parameter('start', $start),
                    new Parameter('end', $end)
                ]
            ))
            ->setFirstResult(($currentPage - 1) * $pageSize)
            ->setMaxResults($pageSize)
        ;
        if ($sort) {
            $qb->orderBy('o.' . $sort['field'], $sort['order'] === 'ascend' ? 'ASC' : 'DESC');
        }
        $filterConditions = [
            'paymentStatus' => 'o.paymentStatus IN (:paymentStatus)',
            'prepareStatus' => 'o.prepareStatus IN (:prepareStatus)',
            'terminal' => 't.id IN (:terminal)',
            'type' => 'o.type IN (:type)',
        ];

        $filterLike = [
            'id' => 'o.id LIKE :id',
        ];

        foreach ($filters as $key => $filter) {
            if ($filter !== null && array_key_exists($key, $filterConditions)) {
                $qb->andWhere($filterConditions[$key])->setParameter($key, $filter);
            }
            if ($filter !== null && array_key_exists($key, $filterLike)) {
                $qb->andWhere($filterLike[$key])->setParameter($key, '%' . $filter[0] . '%');
            }
        }
        //dd($qb->getQuery());
        return $qb->getQuery()->getResult();
    }

    public function getAllCount($start, $end, $search, $filters, $dateType)
    {
        $qb = $this->createQueryBuilder('o');
        $qb->leftJoin('o.terminal', 't');
        $qb
            ->select('COUNT(o.id)')

            ->where('o.' . $dateType . ' BETWEEN :start AND :end')
            ->andWhere(
                $qb->expr()->orX(
                    $qb->expr()->like('o.id', ':search'),
                    $qb->expr()->like('o.paymentStatus', ':search'),
                    $qb->expr()->like('o.prepareStatus', ':search'),
                    $qb->expr()->like('o.createdAt', ':search'),
                    $qb->expr()->like('o.modifiedAt', ':search'),
                    $qb->expr()->like('o.total', ':search'),
                    $qb->expr()->like('o.type', ':search'),
                )
            )
            ->setParameters(new ArrayCollection(
                [
                    new Parameter('search', '%' . $search . '%'),
                    new Parameter('start', $start),
                    new Parameter('end', $end)
                ]
            ))
        ;
        $filterConditions = [
            'paymentStatus' => 'o.paymentStatus IN (:paymentStatus)',
            'prepareStatus' => 'o.prepareStatus IN (:prepareStatus)',
            'terminal' => 't.id IN (:terminal)',
            'type' => 'o.type IN (:type)',
        ];

        $filterLike = [
            'id' => 'o.id LIKE :id',
        ];

        foreach ($filters as $key => $filter) {
            if ($filter !== null && array_key_exists($key, $filterConditions)) {
                $qb->andWhere($filterConditions[$key])->setParameter($key, $filter);
            }
            if ($filter !== null && array_key_exists($key, $filterLike)) {
                $qb->andWhere($filterLike[$key])->setParameter($key, '%' . $filter[0] . '%');
            }
        }
        return (int)$qb->getQuery()->getSingleScalarResult();
    }
}
