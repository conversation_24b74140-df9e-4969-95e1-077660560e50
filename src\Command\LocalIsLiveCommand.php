<?php

namespace App\Command;

use App\Entity\Category;
use App\Entity\Options;
use App\Entity\OptionValues;
use App\Entity\Products;
use App\Entity\Tags;
use App\Entity\User;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\HttpFoundation\File\File;
use Symfony\Component\HttpKernel\KernelInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class LocalIsLiveCommand extends Command
{
    protected static $defaultName = 'global:isLive';
    private $em;
    private $httpClient;
    private $globalServerUrl;
    private $globalApiKey;
    private string $projectDir;

    public function __construct(EntityManagerInterface $em, HttpClientInterface $httpClient, KernelInterface $kernel, string $globalServerUrl, string $globalApiKey)
    {
        $this->em = $em;
        $this->httpClient = $httpClient;
        $this->globalServerUrl = $globalServerUrl;
        $this->globalApiKey = $globalApiKey;
        $this->projectDir = $kernel->getProjectDir();
        parent::__construct();
    }
    protected function configure(): void
    {
        // ...-up
    }

    /**
     * @throws \Exception
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln('Checking if local is live');
        //$admin = $this->em->getRepository(User::class)->getAdmin();
        //dump($admin->toShow());
        $imageToSync = [];
        $products = $this->em->getRepository(Products::class)->findBy(['externalId' => null]);
        $productSync = [];
        foreach ($products as $product) {
            $productSync[] = $product->toArray();
            $imageToSync[] = $product->getImage();
        }
        $categories = $this->em->getRepository(Category::class)->findBy(['externalId' => null]);
        $categorySync = [];
        foreach ($categories as $category) {
            $categorySync[] = $category->toArray();
            if ($category->getImage() != null) {
                $imageToSync[] = $category->getImage();
            }
        }
        $tags = $this->em->getRepository(Tags::class)->findBy(['externalId' => null]);
        $tagSync = [];
        foreach ($tags as $tag) {
            $tagSync[] = $tag->toArray();
        }
        $options = $this->em->getRepository(OptionValues::class)->findBy(['externalId' => null]);
        $optionSync = [];
        foreach ($options as $option) {
            $optionSync[] = $option->toArray();
            if ($option->getImage() != null) {
                $imageToSync[] = $option->getImage();
            }
        }
        //add binary files to sync request
        $multipartData = [];
        $size = 0;
        foreach ($imageToSync as $localFilePath) {
            $imagepath = $this->projectDir . '/public' . $localFilePath;
            if (!file_exists($imagepath)) {
                throw new \Exception("File not found: " . $imagepath);
            }

            $multipartData[$localFilePath] = [
                'contents' => fopen($imagepath, 'r'),
            ];
            $size += filesize($imagepath);
        }
        dump(count($multipartData), $size / 1024 / 1024);

        $response = $this->httpClient->request('POST', $this->globalServerUrl . '/v1/sync/images', [
            'headers' => [
                'api-key' => $this->globalApiKey,
            ],
            'body' => $multipartData,
            'verify_peer' => false,
            "verify_host" => false
        ]);
        //get request size

        dump(count($multipartData), $response->toArray()['data']['imageSize']);
        $response = $this->httpClient->request('POST', $this->globalServerUrl . '/v1/sync/', [
            'headers' => [
                'api-key' => $this->globalApiKey,
            ],
            'body' => [
                'products' => $productSync,
                'categories' => $categorySync,
                'tags' => $tagSync,
                'optionValues' => $optionSync
            ],
            'verify_peer' => false,
            "verify_host" => false
        ]);
        $data = $response->toArray();
        //dump($data);
        return Command::SUCCESS;
    }
}
