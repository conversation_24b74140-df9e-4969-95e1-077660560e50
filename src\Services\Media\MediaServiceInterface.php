<?php

namespace App\Services\Media;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;

interface MediaServiceInterface
{
    public function getAllMedia(): array;

    public function getMediaById(int $id): array;

    public function getMediaByType(string $type): array;

    public function addMedia(Request $request): array;

    public function updateMedia(Request $request, int $id): array;

    public function deleteMedia(int $id): array;

    public function doAction(Request $request): array;
}
