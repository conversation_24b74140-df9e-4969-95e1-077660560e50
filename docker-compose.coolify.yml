version: '3'

services:
  php:
    container_name: BE-Terminal-Local-php
    build:
      context: .
      target: prod
    environment:
      - APP_ENV=${APP_ENV}
      - APP_SECRET=${APP_SECRET}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - JWT_PUBLIC_KEY=${JWT_PUBLIC_KEY}
      - JWT_PASSPHRASE=${JWT_PASSPHRASE}
      - DATABASE_URL=${DATABASE_URL}
    networks:
      - BE-Terminal-Local-Network

  nginx:
    container_name: BE-Terminal-Local-nginx
    build:
      context: .
      target: nginx
    depends_on:
      - php
    networks:
      - BE-Terminal-Local-Network

networks:
  BE-Terminal-Local-Network: