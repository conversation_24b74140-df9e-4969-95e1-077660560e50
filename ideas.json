{
  "categories": [
    {
        "_id": ObjectId,
        "name": String,
        "image": String,
        "parent_id": ObjectId // reference to the parent category
    }
  ],
  "products": [
    {
      "_id": ObjectId,
      "name": String,
      "description": String,
      "image": String,
      "category_id": ObjectId,
      "subCategory_id": ObjectId,
      "isCustomizable": Boolean,
      "isFeatured": Boolean,
      "isAddon": Boolean,
      "isPack": Boolean,
      "mainProductId": ObjectId,
      "price": Double,
      "options": [
        {
          "_id": ObjectId,
          "name": String,
          "isRequired": Boolean,
          "multiple": Boolean,
          "default" : [ObjectId]
          "optionsValues": [
            {
              "_id": ObjectId,
              "image": String,
              "price": Double,
              "name": String
            },
            {
              "_id": ObjectId,
              "image": String,
              "price": Double,
              "name": String
            }
          ]
        }
      ],
      "tags": [
        {
          "_id": ObjectId,
          "name": String,
          "color": String
        }
      ]
    }
  ]
}