<?php

namespace App\Services\OptionValues;

use Symfony\Component\HttpFoundation\Request;

interface OptionValuesServiceInterface
{
    public function getOptionValues(): array;

    public function getOptionValueById(int $id): array;

    public function addOptionValue(Request $request): array;

    public function deleteOptionValue(int $id): array;

    public function updateOptionValue(Request $request, int $id): array;

    public function doAction(Request $request): array;
}
