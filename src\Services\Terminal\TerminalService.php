<?php

namespace App\Services\Terminal;

use App\Entity\Terminals;
use App\Entity\Category;
use App\Entity\Options;
use App\Entity\OptionValues;
use Doctrine\ORM\EntityManagerInterface;
use App\Services\Terminal\TerminalServiceInterface;
use App\Utils\Uploader;
use DateTime;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Security\Core\Security;
use Symfony\Component\HttpFoundation\Response;

class TerminalService implements TerminalServiceInterface
{

    private EntityManagerInterface $em;
    private Security $security;

    public function __construct(EntityManagerInterface $entityManager, Security $security)
    {
        $this->em = $entityManager;
        $this->security = $security;
    }

    public function ping(Request $request): array
    {
        // get Request ip
        $ip = $request->getClientIp();
        $apiKey = $request->headers->get('x-api-key');
        $terminal = $this->em->getRepository(Terminals::class)->findOneBy(['apiKey' => $apiKey]);
        if (!$terminal || $terminal->getStatus() === 'blocked') {
            return [
                'status' => Response::HTTP_UNAUTHORIZED,
                'data' => 'Unauthorized'
            ];
        }
        $terminal->setIp($ip);
        $terminal->setIsOnline(true);
        $this->em->flush();
        return [
            'status' => Response::HTTP_OK,
            'data' => ['pong']
        ];
    }

    public function subscribe(Request $request): array
    {
        return [
            'status' => Response::HTTP_OK,
            'data' => ['subscribe']
        ];
    }
}
