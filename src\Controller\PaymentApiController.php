<?php

namespace App\Controller;

use App\Services\Payment\PaymentServiceInterface;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * @Rest\Route("/payment_methode" , name="payment_methode_" )
 */
class PaymentApiController extends AbstractFOSRestController
{
    private PaymentServiceInterface $paymentService;

    public function __construct(PaymentServiceInterface $paymentService)
    {
        $this->paymentService = $paymentService;
    }

    /**
     * @Rest\Post("/" , name="payment_methode_add" )
     */
    public function add(Request $request)
    {
        $res = $this->paymentService->addMethode($request);
        if ($res['status'] === Response::HTTP_CREATED) {
            $view = $this->view($res["data"], Response::HTTP_CREATED, []);
        } else {
            $view = $this->view($res["data"], $res['status'], []);
        }
        return $this->handleView($view);
    }

    /**
     * @Rest\GET("s/" , name="get_all_payment_method" )
     */
    public function getAll()
    {
        $res = $this->paymentService->getAllMethode();
        if ($res['status'] === Response::HTTP_OK) {
            $view = $this->view($res["data"], Response::HTTP_OK, []);
        } else {
            $view = $this->view($res["data"], $res['status'], []);
        }
        return $this->handleView($view);
    }
    /**
     * @Rest\GET("s/active" , name="get_all_payment_method_active" )
     */
    public function getActiveMethod()
    {
        $res = $this->paymentService->getActiveMethode();
        if ($res['status'] === Response::HTTP_OK) {
            $view = $this->view($res["data"], Response::HTTP_OK, []);
        } else {
            $view = $this->view($res["data"], $res['status'], []);
        }
        return $this->handleView($view);
    }

    /**
     * @Rest\GET("/{id}" , name="get_by_id_payment_method" )
     */
    public function getById(int $id)
    {
        $res = $this->paymentService->getMethode($id);
        if ($res['status'] === Response::HTTP_OK) {
            $view = $this->view($res["data"], Response::HTTP_OK, []);
        } else {
            $view = $this->view($res["data"], $res['status'], []);
        }
        return $this->handleView($view);
    }

    /**
     * @Rest\POST("/status" , name="payment_method_change_status" )
     */
    public function changeStatus(Request $request,)
    {
        $res = $this->paymentService->changeStatus($request);
        if ($res['status'] === Response::HTTP_OK) {
            $view = $this->view($res["data"], Response::HTTP_OK, []);
        } else {
            $view = $this->view($res["data"], $res['status'], []);
        }
        return $this->handleView($view);
    }

    /**
     * @Rest\POST("/update" , name="payment_method_update" )
     */
    public function update(Request $request)
    {
        $res = $this->paymentService->updateMethode($request);
        if ($res['status'] === Response::HTTP_OK) {
            $view = $this->view($res["data"], Response::HTTP_OK, []);
        } else {
            $view = $this->view($res["data"], $res['status'], []);
        }
        return $this->handleView($view);
    }

    /**
     * @Rest\POST("/delete" , name="payment_method_delete" )
     */
    public function delete(Request $request,)
    {
        $res = $this->paymentService->removeMethode($request);
        if ($res['status'] === Response::HTTP_OK) {
            $view = $this->view($res["data"], Response::HTTP_OK, []);
        } else {
            $view = $this->view($res["data"], $res['status'], []);
        }
        return $this->handleView($view);
    }
}
