<?php

namespace App\Command;

use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(name: 'app:import-database')]
class ImportDatabaseFromFileCommand extends Command
{
    protected static $defaultName = 'app:import-database';
    protected static $defaultDescription = 'Restores a backup from the specified file';
    private string $backupDir;

    public function __construct()
    {
        parent::__construct();
        $this->backupDir = __DIR__ . '/../../public/backups/';
    }
    protected function configure(): void
    {
        $this
            ->addArgument('backupFile', null, 'The backup file to restore')
            ->setDescription('Restores a backup from the specified file')
            ->setHelp('This command allows you to restore a backup from a specified file');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $backupFile = $input->getArgument('backupFile');
        $source = $this->backupDir . $backupFile;
        $destination = __DIR__ . '/../../var/data.db';
        if (!file_exists($source)) {
            $output->writeln("<error>Backup file not found.</error>");
            return Command::FAILURE;
        }
        if (!copy($source, $destination)) {
            $output->writeln("<error>Failed to restore backup.</error>");
            return Command::FAILURE;
        }
        $output->writeln("<info>Backup restored successfully.</info>");
        return Command::SUCCESS;
    }
}
