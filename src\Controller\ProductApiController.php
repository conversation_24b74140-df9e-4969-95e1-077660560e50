<?php

namespace App\Controller;

use App\Services\Product\ProductServiceInterface;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * @Rest\Route("/product" , name="product_")
 */
class ProductApiController extends AbstractFOSRestController
{

    private ProductServiceInterface $productService;
    public function __construct(ProductServiceInterface $productService)
    {
        $this->productService = $productService;
    }

    /**
     * @Rest\Get("/" , name="all" )
     */
    public function getAll()
    {
        $res = $this->productService->getAllProducts();
        $view = $this->view($res["data"], $res['status'], []);
        return $this->handleView($view);
    }

    /**
     * @Rest\Post("/doAction" , name="do_action" )
     */
    public function doAction(Request $request)
    {
        $res = $this->productService->doAction($request);
        $view = $this->view($res["data"], $res['status'], []);
        return $this->handleView($view);
    }


    /**
     * @Rest\Get("/{id}" , name="by_id" , requirements={"id"="\d+"})
     */
    public function getById($id)
    {
        $res = $this->productService->getProductById($id);
        $view = $this->view($res["data"], $res['status'], []);
        return $this->handleView($view);
    }

    /**
     * @Rest\Post("/" , name="create" )
     */
    public function create(Request $request)
    {
        $res = $this->productService->addProduct($request);
        $view = $this->view($res["data"], $res['status'], []);
        return $this->handleView($view);
    }

    /**
     * @Rest\Post("/{id}" , name="modify" , requirements={"id"="\d+"})
     */
    public function modify(Request $request, $id)
    {
        $res = $this->productService->updateProduct($request, $id);
        $view = $this->view($res["data"], $res['status'], []);
        return $this->handleView($view);
    }

    /**
     * @Rest\Post("/delete/{id}" , name="delete" , requirements={"id"="\d+"})
     */
    public function delete($id)
    {
        $res = $this->productService->deleteProduct($id);
        $view = $this->view($res["data"], $res['status'], []);
        return $this->handleView($view);
    }
}
