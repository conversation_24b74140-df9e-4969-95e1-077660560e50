<?php

namespace App\Entity;

use App\Repository\PrinterRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: PrinterRepository::class)]
class Printer
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 25)]
    private ?string $ipAdress = null;

    /**
     * @var Collection<int, category>
     */
    #[ORM\OneToMany(targetEntity: Category::class, mappedBy: 'printer')]
    private Collection $category;

    #[ORM\Column]
    private ?bool $isMain = null;

    public function __construct($ipAddress, $isMain = false, $categories = null)
    {
        $this->ipAdress = $ipAddress;
        $this->isMain = $isMain;
        $this->category = $categories ?? new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getIpAdress(): ?string
    {
        return $this->ipAdress;
    }

    public function setIpAdress(string $ipAdress): static
    {
        $this->ipAdress = $ipAdress;

        return $this;
    }

    /**
     * @return Collection<int, category>
     */
    public function getCategory(): Collection
    {
        return $this->category;
    }

    public function addCategory(Category $category): static
    {
        if (!$this->category->contains($category)) {
            $this->category->add($category);
            $category->setPrinter($this);
        }

        return $this;
    }

    public function removeCategory(category $category): static
    {
        if ($this->category->removeElement($category)) {
            // set the owning side to null (unless already changed)
            if ($category->getPrinter() === $this) {
                $category->setPrinter(null);
            }
        }

        return $this;
    }

    public function isMain(): ?bool
    {
        return $this->isMain;
    }

    public function setIsMain(bool $isMain): static
    {
        $this->isMain = $isMain;

        return $this;
    }

    public function toArray()
    {
        return [
            'id' => $this->getId(),
            'isMain' => $this->isMain(),
            'ipAdress' => $this->getIpAdress(),
            'categories' => $this->getCategory()->map(fn(Category $category) => ['id' => $category->getId(), 'name' => $category->getName()])
        ];
    }
}
