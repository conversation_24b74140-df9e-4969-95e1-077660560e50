<?php

namespace App\Command;

use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use ZipArchive;

#[AsCommand(name: 'app:backup-sqlite')]
class BackupSqliteCommand extends Command
{
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $sourceDb = __DIR__ . '/../../var/data.db';
        $imagesDir = __DIR__ . '/../../public/images';
        $videosDir = __DIR__ . '/../../public/videos';
        $backupDir = __DIR__ . '/../../public/backups/';
        if (!is_dir($backupDir)) mkdir($backupDir);

        $timestamp = date('Y-m-d_H-i-s');
        $zipPath = $backupDir . "backup_$timestamp.zip";

        $zip = new ZipArchive();
        if ($zip->open($zipPath, ZipArchive::CREATE) !== true) {
            $output->writeln("<error>Failed to create zip archive.</error>");
            return Command::FAILURE;
        }

        // Add database
        $zip->addFile($sourceDb, "data_$timestamp.db");

        // Add images and videos recursively
        $this->addFolderToZip($imagesDir, $zip, 'images');
        $this->addFolderToZip($videosDir, $zip, 'videos');

        $zip->close();

        $output->writeln("<info>Backup created: $zipPath</info>");
        return Command::SUCCESS;
    }

    private function addFolderToZip(string $folder, ZipArchive $zip, string $zipPathPrefix)
    {
        if (!is_dir($folder)) return;

        $folderRealPath = realpath($folder);

        $files = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($folderRealPath, \FilesystemIterator::SKIP_DOTS),
            \RecursiveIteratorIterator::LEAVES_ONLY
        );

        foreach ($files as $file) {
            if (!$file->isDir()) {
                $filePath = $file->getRealPath();
                $relativePath = $zipPathPrefix . '/' . str_replace('\\', '/', substr($filePath, strlen($folderRealPath) + 1));
                $zip->addFile($filePath, $relativePath);
            }
        }
    }
}
