<?php

namespace App\Command;

use App\WebSocket\Chat;
use Ratchet\Server\IoServer;
use Ratchet\Http\HttpServer;
use Ratchet\WebSocket\WsServer;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class WebSocketServerCommand extends Command
{
    protected static $defaultName = 'websocket:start';

    protected function configure()
    {
        $this->setDescription('Starts the WebSocket server');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $output->writeln("Starting WebSocket server...");

        $chat = new Chat();
        $server = IoServer::factory(
            new HttpServer(
                new WsServer(
                    $chat
                )
            ),
            8080, // Port where the WebSocket server will listen,
            '0.0.0.0'
        );

        $server->run();
        return Command::SUCCESS;
    }
}
