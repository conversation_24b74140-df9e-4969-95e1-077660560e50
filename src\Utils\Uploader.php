<?php



namespace App\Utils;

use Symfony\Component\HttpFoundation\File\UploadedFile;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;

class Uploader
{


    public static function uploadFile(UploadedFile $file, String $dir)
    {
        $originalFilename = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $safeFilename = Uploader::generateSluger($originalFilename);
        $fileName = $safeFilename . '-' . uniqid('', true);

        $manager = new ImageManager(new Driver());
        $image = $manager->read($file);
        // set backgrount to transparent
        //$image->pad(900, 900, 'f0f8ff00');
        //$encoded = $image->toWebp(60);
        $comFilePath = getcwd() . "/images/" . $dir . "/" . $fileName . $file->getClientOriginalExtension(); //TODO::Guess Client Extention
        // create folder if not exist
        if (!file_exists(getcwd() . "/images/" . $dir)) {
            mkdir(getcwd() . "/images/" . $dir, 0777, true);
        }
        $image->save($comFilePath);
        return "/images/" . $dir . "/" . $fileName . $file->getClientOriginalExtension();
    }

    public static function uploadVideo(UploadedFile $file, string $dir)
    {
        // Step 1: Get the original filename and make it safe
        $originalFilename = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $safeFilename = Uploader::generateSluger($originalFilename);
        $fileExtension = $file->guessExtension(); // Get the file extension (e.g., mp4, mov, etc.)
        $fileName = $safeFilename . '-' . uniqid('', true) . '.' . $fileExtension; // Preserve original extension

        // Step 2: Create the directory if it doesn't exist
        $videoDir = getcwd() . "/videos/" . $dir;
        if (!file_exists($videoDir)) {
            mkdir($videoDir, 0777, true);
        }

        // Step 3: Move the uploaded file to the desired directory
        $file->move($videoDir, $fileName);

        // Step 4: Return the relative path to the saved video file
        return "/videos/" . $dir . "/" . $fileName;
    }

    public static function uploadImageToWebp(UploadedFile $file, String $dir)
    {
        $originalFilename = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $safeFilename = Uploader::generateSluger($originalFilename);
        $fileName = $safeFilename . '-' . uniqid('', true);

        $manager = new ImageManager(new Driver());
        $image = $manager->read($file);
        // set backgrount to transparent
        $image->pad(900, 900, 'f0f8ff00');
        $encoded = $image->toWebp(60);
        $comFilePath = getcwd() . "/images/" . $dir . "/" . $fileName . '.webp';
        // create folder if not exist
        if (!file_exists(getcwd() . "/images/" . $dir)) {
            mkdir(getcwd() . "/images/" . $dir, 0777, true);
        }
        $encoded->save($comFilePath);
        return "/images/" . $dir . "/" . $fileName . '.webp';
    }

    public static function deleteFile(String $path)
    {
        if (file_exists(getcwd() . $path)) {
            unlink(getcwd() . $path);
        }
        return true;
    }

    public static function generateSluger(String $text)
    {
        $slug = preg_replace('/[^a-zA-Z0-9]/', '-', $text);
        $slug = strtolower($slug);
        $slug = trim($slug, '-');
        return $slug;
    }
}
