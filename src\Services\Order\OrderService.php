<?php

namespace App\Services\Order;

use App\Entity\Order;
use App\Entity\Terminals;
use App\Entity\Options;
use App\Entity\OptionValues;
use App\Entity\OrderItem;
use App\Entity\Printer;
use App\Entity\Products;
use Doctrine\ORM\EntityManagerInterface;
use App\Services\Order\OrderServiceInterface;
use App\WebSocket\Chat;
use Mike42\Escpos\PrintConnectors\NetworkPrintConnector;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Security\Core\Security;
use Symfony\Component\HttpFoundation\Response;
use Mike42\Escpos\Printer as PrinterLib;
use Symfony\Component\Process\Process;
use Symfony\Contracts\HttpClient\Exception\HttpExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use WebSocket\Client;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class OrderService implements OrderServiceInterface
{

    private EntityManagerInterface $em;
    private Security $security;
    private $wsClient;
    private HttpClientInterface $httpClient;
    private HttpClientInterface $httpClient;

    // public function __construct(EntityManagerInterface $entityManager, Security $security, HttpClientInterface $httpClient)
    public function __construct(EntityManagerInterface $entityManager, Security $security, HttpClientInterface $httpClient)
    {
        $this->em = $entityManager;
        $this->security = $security;
        $this->httpClient = $httpClient;

        $this->wsClient = new Client("ws://192.168.1.165:8080");
        $this->httpClient = $httpClient;

        $this->wsClient = new Client("ws://192.168.1.165:8080");
    }

    public function create(Request $request): array
    {
        $terminalId = $request->headers->get('X-Api-Key');
        $terminal = $this->em->getRepository(Terminals::class)->findOneBy(['apiKey' => $terminalId]);
        if (!$terminal) {
            return [
                'status' => Response::HTTP_UNAUTHORIZED,
                'data' => 'Cant make this Order'
            ];
        }
        if (!$request->get('items') || sizeof($request->get('items')) <= 0) {
            return [
                'status' => Response::HTTP_BAD_REQUEST,
                'data' => 'Items Are Requierd'
            ];
        }

        $order = new Order();
        $order->setTerminal($terminal);
        // for now it is Cache On Counter so it is pending will add
        // payment methods and payment status will be fetched from there
        $order->setPaymentStatus('pending');
        $order->setType($request->get('type') ?: 'dineIn');
        $order->setOrderTable($request->get('table') ?: null);
        $order->setOrderTable($request->get('table') ?: null);
        $totalPrice = 0;
        foreach ($request->get('items') as $item) {
            $productId = $item['id']; // check for - [0]
            $product = $this->em->getRepository(Products::class)->find($productId);
            if (!$product) {
                return [
                    'status' => Response::HTTP_BAD_REQUEST,
                    'data' => 'A product is not found'
                ];
            }
            $price = $product->getPrice() * $item['quantity'];
            $selectedOptions = array_key_exists('selectedOptions', $item) ? $item['selectedOptions'] : [];
            if ($product->isIsCustomizable()) {
                foreach ($selectedOptions as $selectedOption) {
                    $optionValue = $this->em->getRepository(OptionValues::class)->find($selectedOption['id']);
                    //TODO::re-verify after the complition of add Product
                    $price += ($optionValue->getPrice() * $selectedOption['quantity']);
                    $optionValue = $this->em->getRepository(OptionValues::class)->find($selectedOption['id']);
                    //TODO::re-verify after the complition of add Product
                    $price += ($optionValue->getPrice() * $selectedOption['quantity']);
                }
            }

            $orderItem = new OrderItem();
            $orderItem->setProduct($product);
            $orderItem->setQuantity($item['quantity']);
            $orderItem->setPrice($price);
            $orderItem->setRef($item['id']);
            $orderItem->setOptions($selectedOptions);
            $totalPrice += $price;
            $order->addOrderItem($orderItem);
            $this->em->persist($orderItem);
        }
        $order->setTotal($totalPrice);




        $this->em->persist($order);
        $this->em->flush();

        try {
            if ($terminal->getApikey() === 'pos-terminal-main') {
                $this->updatePaymentStatus($order->getId(), 'paid');
            } else {
                $this->wsClient->send(json_encode(['type' => 'new_order', 'order_id' => $order->getId()]));
            }
        } catch (\Exception $e) {
        }

        try {
            //check if the url is reachable
            // This is to ensure that the API is up before sending the request

            $this->httpClient->request('HEAD', "http://127.0.0.1:8000/v1/order/");

            $response = $this->httpClient->request('POST', 'http://127.0.0.1:8000/v1/order/', [
                'headers' => [
                    'X-Api-Key' => '5c01c1659234d769d301cc867a667c8c9d1a9530dedea49975e5f801f78a3e23',
                ],
                'json' => [
                    'data' => $order->toArray(),
                ]
            ]);
        } catch (TransportExceptionInterface $e) {
        }


        return [
            'status' => Response::HTTP_CREATED,
            'data' => $order->toArray()
        ];
    }

    public function getAll(Request $request): array
    {
        $start = $request->get('filters')['start'] ? new \DateTime($request->get('filters')['start']) : new \DateTime('last day of last month midnight');
        $end = $request->get('filters')['end'] ? new \DateTime($request->get('filters')['end']) : new \DateTime('last day of this month midnight');
        $currentPage = $request->get('pagination')['current'] ? $request->get('pagination')['current'] : 1;
        $pageSize = $request->get('pagination')['pageSize'] ? $request->get('pagination')['pageSize'] : 10;
        $search = $request->get('search') ? $request->get('search') : null;
        $sort = $request->get('field') && $request->get('order') ? ['field' => $request->get('field'), 'order' => $request->get('order')] : ['field' => 'id', 'order' => 'descend'];
        $sort = $request->get('field') && $request->get('order') ? ['field' => $request->get('field'), 'order' => $request->get('order')] : ['field' => 'id', 'order' => 'descend'];
        $filters = $request->get('filters') ? $request->get('filters') : null;
        $dateType = $request->get('dateType') ? $request->get('dateType') : 'createdAt';
        if ($start === $end) {
            $start->modify('first day of ' . $start->format('F') . ' ' . $start->format('Y') . ' 00:00:00');
            $end->modify('last day of ' . $end->format('F') . ' ' . $end->format('Y') . ' 23:59:59');
        }
        unset($filters['start']);
        unset($filters['end']);
        $orders = $this->em->getRepository(Order::class)->getAll($start, $end, $search, $currentPage, $pageSize, $sort, $filters, $dateType);
        $allOrdersCount = $this->em->getRepository(Order::class)->getAllCount($start, $end, $search, $filters, $dateType);
        $info = [
            'total' => $allOrdersCount,
            'current' => $currentPage,
            'pageSize' => $pageSize,
            'pageSizeOptions' => ['25', '50', '100', '500', '1000'],
            'totalPage' => ceil($allOrdersCount / $pageSize)
        ];
        return [
            'status' => Response::HTTP_OK,
            'data' => [
                'info' => $info,
                'results' => $orders,
            ],
        ];
        /* $res = $this->em->getRepository(Order::class)->findAll();
        $orders = [];
        foreach ($res as $order) {
            $orders[] = $order->toArray();
        }
        return [
            'status' => Response::HTTP_OK,
            'data' => $orders
        ]; */
    }

    public function getById(int $id): array
    {
        $res = $this->em->getRepository(Order::class)->find($id);
        if (!$res) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'data' => 'Order not found'
            ];
        }

        return [
            'status' => Response::HTTP_OK,
            'data' => $res->toArray()
        ];
    }

    public function getByPaymentStatus(string $status): array
    public function getByPaymentStatus(string $status): array
    {
        if ($status === 'completed') {
            $res = $this->em->getRepository(Order::class)->findBy(['prepareStatus' => $status, 'paymentStatus' => 'paid']);
        } else {
            $res = $this->em->getRepository(Order::class)->findBy(['paymentStatus' => $status]);
        }
        $orders = [];
        foreach ($res as $order) {
            $orders[] = $order->toArray();
        }

        return [
            'status' => Response::HTTP_OK,
            'data' => $orders
        ];
    }

    public function getByPrepareStatus(string $status): array
    public function getByPrepareStatus(string $status): array
    {
        $res = $this->em->getRepository(Order::class)->findBy(['prepareStatus' => $status, 'paymentStatus' => 'paid']);
        $orders = [];
        foreach ($res as $order) {
            $orders[] = $order->toArray();
        }

        return [
            'status' => Response::HTTP_OK,
            'data' => $orders
        ];
    }

    public function updatePaymentStatus(int $id, string $status): array
    public function updatePaymentStatus(int $id, string $status): array
    {
        $res = $this->em->getRepository(Order::class)->find($id);
        if (!$res) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'data' => 'Order not found'
            ];
        }
        $res->setPaymentStatus($status);
        if ($status === 'paid') {
            try {
                $mainPrinter = $this->em->getRepository(Printer::class)->findOneBy(['isMain' => true]);
                if (!$mainPrinter) {
                    return [
                        'status' => Response::HTTP_NOT_ACCEPTABLE,
                        'data' => 'Main Printer Not Found'
                    ];
                    return [
                        'status' => Response::HTTP_NOT_ACCEPTABLE,
                        'data' => 'Main Printer Not Found'
                    ];
                }
                $consolePath = realpath(getcwd() . '/../bin/console');
                $process = new Process(['php', $consolePath, 'payment:print', $id]);

                // $process->start();
                // $process->start();
                $process->run();
                // executes after the command finishes
                if (!$process->isSuccessful()) {
                    return [
                        'status' => Response::HTTP_NOT_ACCEPTABLE,
                        'data' => "error" . json_encode($process->getErrorOutput()) . json_decode($process->getOutput())
                        'status' => Response::HTTP_NOT_ACCEPTABLE,
                        'data' => "error" . json_encode($process->getErrorOutput()) . json_decode($process->getOutput())
                    ];
                }
                $this->wsClient->send(json_encode(['type' => 'order_paid', 'order_id' => $id]));
            } catch (\Exception $e) {
            }

            //$this->chat->sendOrderNotification($id);
        }
        $this->em->flush();
        return [
            'status' => Response::HTTP_OK,
            'data' => $res->toArray()
        ];
    }

    public function updatePrepareStatus(int $id, string $status): array
    public function updatePrepareStatus(int $id, string $status): array
    {
        $res = $this->em->getRepository(Order::class)->find($id);
        if (!$res) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'data' => 'Order not found'
            ];
        }
        $res->setPrepareStatus($status);
        if ($status === 'proccessing') {
            $consolePath = realpath(getcwd() . '/../bin/console');
            $process = new Process(['php', $consolePath, 'order:print', $id]);


            $process->start();
            //$process->run();
            // executes after the command finishes
            //if (!$process->isSuccessful()) {
            //    return [
            //        'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
            //        'data' => "error" . json_encode($process->getErrorOutput()) . json_decode($process->getOutput())
            //    ];
            //}
        }
        if ($status === 'completed') {
            try {
                $this->wsClient->send(json_encode(['type' => 'order_completed', 'order_id' => $id]));
            } catch (\Exception $e) {
                //dump($e);
                //dump($e);
            }
        }
        $this->em->flush();
        return [
            'status' => Response::HTTP_OK,
            'data' => $res->toArray(),
        ];
    }
}
