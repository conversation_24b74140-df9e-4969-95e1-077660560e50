<?php

namespace App\Services\Product;

use App\Entity\Products;
use App\Entity\Category;
use App\Entity\Options;
use App\Entity\Tags;
use App\Entity\OptionValues;
use Doctrine\ORM\EntityManagerInterface;
use App\Services\Category\CategoryServiceInterface;
use App\Utils\Uploader;
use DateTime;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Security\Core\Security;
use Symfony\Component\HttpFoundation\Response;

class ProductService implements ProductServiceInterface
{

    private EntityManagerInterface $em;
    private Security $security;

    public function __construct(EntityManagerInterface $entityManager, Security $security)
    {
        $this->em = $entityManager;
        $this->security = $security;
    }

    public function getProductById(int $id): array
    {
        $product = $this->em->getRepository(Products::class)->find($id);
        if (!$product) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'data' => 'product not found'
            ];
        }
        if ($product->isDeleted()) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'data' => 'product not found'
            ];
        }
        $drinksCategory = $this->em->getRepository(Category::class)->findOneBy(['name' => 'drinks']);

        $addons = $this->em->getRepository(Products::class)->findBy(['isAddon' => true, 'category' => $drinksCategory]);
        if ($product->isDeleted()) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'data' => 'product not found'
            ];
        }
        $drinksCategory = $this->em->getRepository(Category::class)->findOneBy(['name' => 'drinks']);

        $addons = $this->em->getRepository(Products::class)->findBy(['isAddon' => true, 'category' => $drinksCategory]);
        $result = [];
        shuffle($addons);
        $addonsArray = array_slice($addons, 0, 8);
        shuffle($addons);
        $addonsArray = array_slice($addons, 0, 8);
        foreach ($addonsArray as $addon) {
            $result[] = $addon->toArray();
        }
        $packs = $this->em->getRepository(Products::class)->findBy(['isPack' => true, "mainProduct" => $id]);
        $resultPack = [];
        foreach ($packs as $pack) {
            $resultPack[] = $pack->toArray();
        }
        return [
            'status' => Response::HTTP_OK,
            'data' => [...$product->toArray(), 'addons' => $result, 'packs' => $resultPack]
        ];
    }

    public function getAllProducts(): array
    {
        $products = $this->em->getRepository(Products::class)->findAll();
        $result = [];
        foreach ($products as $product) {
            if ($product->isDeleted()) {
                continue; // skip deleted products
            }
            if ($product->isDeleted()) {
                continue; // skip deleted products
            }
            $result[] = $product->toArray();
        }
        return [
            'status' => Response::HTTP_OK,
            'data' => array_reverse($result) // reverse $result
        ];
    }

    public function getAllFeaturedProducts(): array
    {
        $products = $this->em->getRepository(Products::class)->findBy(['isFeatured' => true]);
        $result = [];
        foreach ($products as $product) {
            $result[] = $product->toArray();
        }
        return [
            'status' => Response::HTTP_OK,
            'data' => $result
        ];
    }

    public function getAddonsByCategory(): array
    {
        $desertCategory = $this->em->getRepository(Category::class)->findOneBy(['name' => 'sweets']);
        $productAddons = $this->em->getRepository(Products::class)->findBy(['isAddon' => true, 'isCustomizable' => false, 'category' => $desertCategory]);
        $desertCategory = $this->em->getRepository(Category::class)->findOneBy(['name' => 'sweets']);
        $productAddons = $this->em->getRepository(Products::class)->findBy(['isAddon' => true, 'isCustomizable' => false, 'category' => $desertCategory]);
        $result = [];
        /*{
        name: 'Drinks', 
        addons: [
            {
                "id": 28,
                "name": "Coca Cola",
                "description": "Soda Drink Cannes",
                "image": "/demo/product_3.png",
                "category": "Drinks",
                "subCategory": "Cannes",
                "isCustomizable": false,
                "isAddon": true,
                "price": 1.8,
                "tags": []
            }
        ]
        }*/
        foreach ($productAddons as $addon) {
            $category = $addon->getCategory();
            if ($category) {
                $categoryName = $category->getName();
                if (!isset($result[$categoryName])) {
                    $result[$categoryName] = [
                        'name' => $categoryName,
                        'addons' => []
                    ];
                }
                $result[$categoryName]['addons'][] = $addon->toArray();
            }
        }
        // remove keys
        $result = array_values($result);
        // sort by name
        usort($result, function ($a, $b) {
            return strcmp($a['name'], $b['name']);
        });
        return [
            'status' => Response::HTTP_OK,
            'data' => $result
        ];
    }


    public function addProduct(Request $request): array
    {
        $product = $this->em->getRepository(Products::class)->findOneBy(['name' => $request->get('name')]);
        if ($product) {
            return [
                'status' => Response::HTTP_BAD_REQUEST,
                'data' => 'product already exists'
            ];
        }
        if ($request->get('isCustomizable') == 'true' && $request->get('isAddon') == 'true') {
            return [
                'status' => Response::HTTP_BAD_REQUEST,
                'data' => 'customizable product cannot be an addon'
            ];
        }
        $product = new Products();
        $product->setName($request->get('name'));
        $product->setDeleted(false);
        $product->setDeleted(false);
        $product->setDescription($request->get('description'));
        $product->setCreatedAt(new DateTime());
        $product->setModifiedAt(new DateTime());
        if ($request->get('price') === null || floatval($request->get('price')) < 0) {
            return [
                'status' => Response::HTTP_BAD_REQUEST,
                'data' => 'price cannot be negative'
            ];
        }
        $product->setPrice(floatval($request->get('price')));
        $product->setCost(floatval($request->get('cost', 0)));
        $product->setTax(floatval($request->get('tax', 0)));
        $product->setDiscount(floatval($request->get('discount', 0)));
        $product->setStatus($request->get('status'));
        $product->setSUK($request->get('suk'));
        $product->setPreparationTime(intval($request->get('duration', 1)));
        $product->setUnit($request->get('unit'));
        $product->setUnitValue($request->get('unitValue'));
        $product->setAttributes(json_decode($request->get('attributes'), true));
        $product->setIngredients(json_decode($request->get('ingredients'), true));
        $product->setIsFeatured($request->get('isFeatured') == 'true');
        $product->setExternalId(12345678);
        if ($request->get('tag') != 'undefined') {
            $tagsId = json_decode($request->get('tag'), true);
            if (is_array($tagsId)) {
                foreach ($tagsId as $tagId) {
                    $tag = $this->em->getRepository(Tags::class)->find($tagId);
                    $product->addTag($tag);
                }
            } else {
                $tag = $this->em->getRepository(Tags::class)->find($request->get('tag'));
                $product->addTag($tag);
            }
        }
        $product->setIsCustomizable($request->get('isCustomizable') == 'true');
        if ($request->get('isStockable') === 'true') {
            $product->setStockable(true);
            if ($request->get('initalStock') === null || intval($request->get('initalStock')) < 0 || intval($request->get('stockThreshold')) < 0) {
                return [
                    'status' => Response::HTTP_BAD_REQUEST,
                    'data' => 'stock or stock threshold cannot be negative'
                ];
            }
            $product->setStock(intval($request->get('initalStock')));
            $product->setStockThreshold(intval($request->get('stockThreshold', 9)));
        } else {
            $product->setStockable(false);
        }
        if ($request->get('isCustomizable') == 'true') {
            $options = json_decode($request->get('options'), true);
            foreach ($options as $option) {
                $step = new Options();
                $step->setName($option['name']);
                if ($option['isRequired'] && $option['isMultiple']) {
                    return [
                        'status' => Response::HTTP_BAD_REQUEST,
                        'data' => 'option is required and multiple'
                    ];
                }
                $step->setIsRequired($option['isRequired']);
                $step->setIsMultiple($option['isMultiple']);
                if (array_key_exists('hasQuantity', $option)) {
                    $step->setHasQuantity($option['hasQuantity']);
                }
                foreach ($option['optionValues'] as $value) {
                    $stepValue = $this->em->getRepository(OptionValues::class)->find($value);
                    if (!$stepValue) {
                        return [
                            'status' => Response::HTTP_BAD_REQUEST,
                            'data' => 'option value not found'
                        ];
                    }
                    $step->addOptionValue($stepValue);
                }
                if ($option['default']) {
                    //check if it is an array 
                    if (!is_array($option['default'])) {
                        $stepDefaultValue = $this->em->getRepository(OptionValues::class)->find($option['default']);
                        if (!$stepDefaultValue) {
                            return [
                                'status' => Response::HTTP_BAD_REQUEST,
                                'data' => 'option value not found'
                            ];
                        }
                        $step->addDefaultOptionsValue($stepDefaultValue);
                    } else {
                        foreach ($option['default'] as $defaultValue) {
                            $stepDefaultValue = $this->em->getRepository(OptionValues::class)->find($defaultValue);
                            if (!$stepDefaultValue) {
                                return [
                                    'status' => Response::HTTP_BAD_REQUEST,
                                    'data' => 'option value not found'
                                ];
                            }
                            $step->addDefaultOptionsValue($stepDefaultValue);
                        }
                    }
                }
                $product->addOption($step);
                $this->em->persist($step);
            }
        }
        $product->setIsAddon($request->get('isAddon') == 'true');
        $product->setIsPack($request->get('isPack') == 'true');
        if ($request->get('isPack') == 'true') {
            $packs = json_decode($request->get('subProducts'), true);
            foreach ($packs as $pack) {
                $subProduct = $this->em->getRepository(Products::class)->find($pack['id']);
                if (!$subProduct) {
                    return [
                        'status' => Response::HTTP_BAD_REQUEST,
                        'data' => 'sub product not found'
                    ];
                }
                $product->addSubProduct($subProduct);
                $product->addSubProduct($subProduct);
                if ($pack['isMainProduct'] === true) {
                    $product->setMainProduct($subProduct);
                }
            }
        }

        $category = $this->em->getRepository(Category::class)->find($request->get('category'));
        if (!$category) {
            return [
                'status' => Response::HTTP_BAD_REQUEST,
                'data' => 'category not found'
            ];
        }
        $product->setCategory($category);
        if ($request->get('subCategory') && $request->get('subCategory') !== 'none') {
            $subCategory = $this->em->getRepository(Category::class)->find($request->get('subCategory'));
            if (!$subCategory || !$subCategory->getParent() || $subCategory->getParent()->getId() != $category->getId()) {
                return [
                    'status' => Response::HTTP_BAD_REQUEST,
                    'data' => 'sub category not found'
                ];
            }
            $product->setSubCategory($subCategory);
        }

        if (!$request->files->get('image')) {
            return [
                'status' => Response::HTTP_BAD_REQUEST,
                'data' => 'Image is required'
            ];
        }

        $image = $request->files->get('image');
        $imageLink = Uploader::uploadImageToWebp($image, 'products');
        $product->setImage($imageLink);
        $product->setCreatedBy($this->security->getUser()->getUserIdentifier());
        $product->setModifedBy($this->security->getUser()->getUserIdentifier());
        $this->em->persist($product);
        $this->em->flush();
        return [
            'status' => Response::HTTP_CREATED,
            'data' => $product->toArray()
        ];
    }

    public function updateProduct(Request $request, $id): array
    {
        $product = $this->em->getRepository(Products::class)->find($id);
        if (!$product) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'data' => 'product not found'
            ];
        }
        $product->setName($request->get('name'));
        $product->setDescription($request->get('description'));
        $product->setStatus($request->get('status'));
        $product->setCreatedAt(new DateTime());
        $product->setModifiedAt(new DateTime());
        if ($request->get('price') === null || floatval($request->get('price')) < 0) {
            return [
                'status' => Response::HTTP_BAD_REQUEST,
                'data' => 'price cannot be negative'
            ];
        }
        $product->setPrice(floatval($request->get('price')));
        $product->setIsFeatured($request->get('isFeatured') == 'true');
        $product->setIsCustomizable($request->get('isCustomizable') == 'true');
        if ($request->get('isCustomizable') == 'true') {
            foreach ($product->getOptions() as $option) {
                $product->removeOption($option);
            }
            $options = $request->get('options');
            foreach ($options as $option) {
                $step = new Options();
                $step->setName($option['name']);
                $step->setIsRequired($option['isRequired']);
                $step->setIsMultiple($option['isMultiple']);
                if (array_key_exists('hasQuantity', $option)) {
                    $step->setHasQuantity($option['hasQuantity']);
                }
                foreach ($option['optionValues'] as $value) {
                    $stepValue = $this->em->getRepository(OptionValues::class)->find($value);
                    if (!$stepValue) {
                        return [
                            'status' => Response::HTTP_BAD_REQUEST,
                            'data' => 'option value not found'
                        ];
                    }
                    $step->addOptionValue($stepValue);
                }
                if ($option['default']) {
                    if (!is_array($option['default'])) {
                        $stepDefaultValue = $this->em->getRepository(OptionValues::class)->find($option['default']);
                    if (!is_array($option['default'])) {
                        $stepDefaultValue = $this->em->getRepository(OptionValues::class)->find($option['default']);
                        if (!$stepDefaultValue) {
                            return [
                                'status' => Response::HTTP_BAD_REQUEST,
                                'data' => 'option value not found'
                            ];
                        }
                        $step->addDefaultOptionsValue($stepDefaultValue);
                    } else {
                        foreach ($option['default'] as $defaultValue) {
                            $stepDefaultValue = $this->em->getRepository(OptionValues::class)->find($defaultValue);
                            if (!$stepDefaultValue) {
                                return [
                                    'status' => Response::HTTP_BAD_REQUEST,
                                    'data' => 'option value not found'
                                ];
                            }
                            $step->addDefaultOptionsValue($stepDefaultValue);
                        }
                    } else {
                        foreach ($option['default'] as $defaultValue) {
                            $stepDefaultValue = $this->em->getRepository(OptionValues::class)->find($defaultValue);
                            if (!$stepDefaultValue) {
                                return [
                                    'status' => Response::HTTP_BAD_REQUEST,
                                    'data' => 'option value not found'
                                ];
                            }
                            $step->addDefaultOptionsValue($stepDefaultValue);
                        }
                    }
                }
                $product->addOption($step);
                $this->em->persist($step);
            }
        }
        $product->setIsAddon($request->get('isAddon') == 'true');
        $product->setIsPack($request->get('isPack') == 'true');

        if ($request->get('isPack') == 'true') {
            $packs = $request->get('subProducts');
            foreach ($product->getSubProducts() as $subProduct) {
                $product->removeSubProduct($subProduct);
            }
            foreach ($packs as $pack) {
                $subProduct = $this->em->getRepository(Products::class)->find($pack['id']);
                if (!$subProduct) {
                    return [
                        'status' => Response::HTTP_BAD_REQUEST,
                        'data' => 'sub product not found'
                    ];
                }
                $product->addSubProduct($subProduct);
                if ($pack['isMainProduct'] === true) {
                    $product->setMainProduct($subProduct);
                }

        if ($request->get('isPack') == 'true') {
            $packs = $request->get('subProducts');
            foreach ($product->getSubProducts() as $subProduct) {
                $product->removeSubProduct($subProduct);
            }
            foreach ($packs as $pack) {
                $subProduct = $this->em->getRepository(Products::class)->find($pack['id']);
                if (!$subProduct) {
                    return [
                        'status' => Response::HTTP_BAD_REQUEST,
                        'data' => 'sub product not found'
                    ];
                }
                $product->addSubProduct($subProduct);
                if ($pack['isMainProduct'] === true) {
                    $product->setMainProduct($subProduct);
                }
            }
        }
        }

        $category = $this->em->getRepository(Category::class)->find($request->get('category'));
        if (!$category) {
            return [
                'status' => Response::HTTP_BAD_REQUEST,
                'data' => 'category not found'
            ];
        }
        $product->setCategory($category);
        if ($request->get('subCategory') && $request->get('subCategory') !== 'none' && $request->get('subCategory') !== 'undefined') {
            $subCategory = $this->em->getRepository(Category::class)->find($request->get('subCategory'));
            if (!$subCategory || !$subCategory->getParent() || $subCategory->getParent()->getId() != $category->getId()) {
                return [
                    'status' => Response::HTTP_BAD_REQUEST,
                    'data' => 'sub category not found'
                ];
            }
            $product->setSubCategory($subCategory);
        }

        $image = $request->files->get('image');
        if ($image) {
            $imageLink = Uploader::uploadImageToWebp($image, 'products');
            $product->setImage($imageLink);
        }

        $product->setModifiedAt(new DateTime());
        $product->setModifedBy($this->security->getUser()->getUserIdentifier());
        $this->em->flush();
        return [
            'status' => Response::HTTP_CREATED,
            'data' => $product->toArray()
        ];
    }

    public function updatePrductImage(Request $request, int $id): array
    {
        $product = $this->em->getRepository(Products::class)->find($id);
        if (!$product) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'data' => 'product not found'
            ];
        }
        if ($request->files->get('image')) {
            $oldImage = $product->getImage();
            $image = $request->files->get('image');
            $imageLink = Uploader::uploadImageToWebp($image, 'products');
            $product->setImage($imageLink);
            if ($oldImage) {
                Uploader::deleteFile($oldImage);
            }
            $this->em->flush();
            return [
                'status' => Response::HTTP_OK,
                'data' => ['product image updated successfully']
            ];
        }
        return [
            'status' => Response::HTTP_NOT_FOUND,
            'data' => 'file not found'
        ];
        return [
            'status' => Response::HTTP_NOT_FOUND,
            'data' => 'file not found'
        ];
    }

    public function deleteProduct(int $id): array
    {
        $product = $this->em->getRepository(Products::class)->find($id);
        if (!$product) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'data' => 'product not found'
            ];
        }
        $product->setDeleted(true);
        // Uploader::deleteFile($product->getImage());
        // $this->em->remove($product);
        $product->setDeleted(true);
        // Uploader::deleteFile($product->getImage());
        // $this->em->remove($product);
        $this->em->flush();
        return [
            'status' => Response::HTTP_OK,
            'data' => ['product deleted successfully']
            'data' => ['product deleted successfully']
        ];
    }


    public function doAction(Request $request): array
    {
        $action = $request->get('action');
        if (!$action) {
            return [
                'status' => Response::HTTP_BAD_REQUEST,
                'data' => 'action is required'
            ];
        }
        switch ($action) {
            case 'getFeaturedProducts':
                return $this->getAllFeaturedProducts();
            case 'changeProductImage':
                return $this->updatePrductImage($request, $request->get('id'));
            case 'getAddonsByCategory':
                return $this->getAddonsByCategory();
            case 'getAddonsByCategory':
                return $this->getAddonsByCategory();
            default:
                return [
                    'status' => Response::HTTP_BAD_REQUEST,
                    'data' => 'action not found'
                ];
        }
    }
}
