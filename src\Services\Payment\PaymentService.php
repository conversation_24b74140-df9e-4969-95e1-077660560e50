<?php

namespace App\Services\Payment;

use App\Entity\PaymentMethode;
use App\Entity\User;
use App\Entity\Media;
use Doctrine\ORM\EntityManagerInterface;
use App\Services\Payment\PaymentServiceInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Security\Core\Security;
use Symfony\Component\HttpFoundation\Response;
use App\Utils\Uploader;

class PaymentService implements PaymentServiceInterface
{
    private $em;
    private $passwordHasher;
    private $security;

    public function __construct(
        EntityManagerInterface $entityManager,
        UserPasswordHasherInterface $passwordHasher,
        Security $security,
    ) {
        $this->em = $entityManager;
        $this->passwordHasher = $passwordHasher;
        $this->security = $security;
    }

    public function addMethode(Request $request)
    {
        $user = $this->em->getRepository(User::class)->find($this->security->getUser());
        if (!$user) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'data' => 'user not found'
            ];
        }
        if (!$this->security->isGranted('ROLE_ADMIN')) {
            return [
                'status' => Response::HTTP_FORBIDDEN,
                'data' => "You are not allowed."
            ];
        }
        $newMethode = new PaymentMethode();
        $newMethode->setName($request->get('name'));
        $newMethode->setStatus(0);
        $newMethode->setAttribut(json_decode($request->get('attribut'), true));

        if ($request->files->get('image')) {
            $imagelink = Uploader::uploadImageToWebp($request->files->get('image'), 'payment');
            $newMethode->setImage($imagelink);
        }

        $this->em->persist($newMethode);
        $this->em->flush();
        return [
            'status' => Response::HTTP_OK,
            'data' => $newMethode
        ];
    }
    public function updateMethode(Request $request)
    {
        $user = $this->em->getRepository(User::class)->find($this->security->getUser());
        if (!$user) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'data' => 'user not found'
            ];
        }
        if (!$this->security->isGranted('ROLE_ADMIN')) {
            return [
                'status' => Response::HTTP_FORBIDDEN,
                'data' => "You are not allowed."
            ];
        }

        $Methode = $this->em->getRepository(PaymentMethode::class)->find($request->get('id'));
        $Methode->setName($request->get('name') ? $request->get('name') : $Methode->getName());
        $Methode->setAttribut($request->get('attribut') ? json_decode($request->get('attribut'), true) : $Methode->getAttribut());

        if ($request->files->get('image')) {
            $oldImage = $Methode->getImage();
            $imagelink = Uploader::uploadImageToWebp($request->files->get('image'), 'payment');
            $Methode->setImage($imagelink);
            if ($oldImage) {
                Uploader::deleteFile($oldImage);
            }
        }

        $this->em->flush();
        return [
            'status' => Response::HTTP_OK,
            'data' => ["Methode updated"]
        ];
    }
    public function getMethode(int $id)
    {
        $Methode = $this->em->getRepository(PaymentMethode::class)->find($id);
        return [
            'status' => Response::HTTP_OK,
            'data' => $Methode
        ];
    }
    public function changeStatus(Request $request)
    {
        $methode = $this->em->getRepository(PaymentMethode::class)->findOneBy(['id' => $request->get('id')]);
        if (!$methode) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'data' => "Methode not found"
            ];
        }

        if ($methode->isStatus()) {
            $methode->setStatus(0);
        } else {
            $methode->setStatus(1);
        }

        $this->em->flush();
        return [
            'status' => Response::HTTP_OK,
            'data' => ["status changed"]
        ];
    }
    public function getAllMethode()
    {
        $methodes = $this->em->getRepository(PaymentMethode::class)->findAll();
        if (count($methodes) == 0) {
            $methode = new PaymentMethode();
            $methode->setName('Default');
            $methode->setStatus(1);
            $methode->setAttribut([]);
            $methode->setImage(null);
            $methode->setIsDefault(true);
            $this->em->persist($methode);
            $this->em->flush();
            return [
                'status' => Response::HTTP_OK,
                'data' => [$methode]
            ];
        }
        $array = [];
        foreach ($methodes as $methode) {
            $array[] = $methode;
        }
        return [
            'status' => Response::HTTP_OK,
            'data' => $array
        ];
    }

    public function getActiveMethode()
    {
        $methodes = $this->em->getRepository(PaymentMethode::class)->findBy(['status' => 1]);
        $array = [];
        foreach ($methodes as $methode) {
            $array[] = $methode;
        }
        return [
            'status' => Response::HTTP_OK,
            'data' => $array
        ];
    }

    public function removeMethode(Request $request)
    {
        $user = $this->em->getRepository(User::class)->find($this->security->getUser());
        if (!$user) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'data' => 'user not found'
            ];
        }
        if (!$this->security->isGranted('ROLE_ADMIN')) {
            return [
                'status' => Response::HTTP_FORBIDDEN,
                'data' => "You are not allowed."
            ];
        }

        $methode = $this->em->getRepository(PaymentMethode::class)->find($request->get('id'));
        if (!$methode) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'data' => "Methode not found"
            ];
        }

        $this->em->remove($methode);
        $this->em->flush();

        return [
            'status' => Response::HTTP_OK,
            'data' => ["Methode removed"]
        ];
    }
}
