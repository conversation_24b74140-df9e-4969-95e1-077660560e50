<?php

namespace App\Services\Product;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;

interface ProductServiceInterface
{
    public function getAllProducts(): array;

    public function getProductById(int $id): array;

    public function addProduct(Request $request): array;

    public function updateProduct(Request $request, int $id): array;

    public function deleteProduct(int $id): array;

    public function doAction(Request $request): array;
}
