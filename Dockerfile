FROM mlocati/php-extension-installer:2 AS php-extension-installer
FROM composer/composer:2-bin AS composer
FROM php:8.2-fpm-alpine AS php


FROM php AS install-dev

WORKDIR /var/www/html

COPY --from=php-extension-installer --link /usr/bin/install-php-extensions /usr/local/bin/
COPY --from=composer --link /composer /usr/local/bin/composer

RUN set -eux; \
    apk update && apk add --no-cache git vim nano shadow; \
    install-php-extensions pdo_pgsql;

COPY .docker/dev.php-fpm.conf /usr/local/etc/php-fpm.d/www.conf
RUN usermod -u 1000 www-data && groupmod -g 1000 www-data

FROM install-dev AS dev

EXPOSE 9000
USER www-data
ENTRYPOINT [".docker/dev.entrypoint.sh"]

FROM php AS install-prod

WORKDIR /var/www/html

COPY --from=php-extension-installer --link /usr/bin/install-php-extensions /usr/local/bin/
COPY --from=composer --link /composer /usr/local/bin/composer

RUN set -eux; \
    install-php-extensions pdo_pgsql;

USER www-data
COPY --link --chown=82:82 . .
COPY .docker/prod.php-fpm.conf /usr/local/etc/php-fpm.d/www.conf

RUN composer install --optimize-autoloader --prefer-dist --no-scripts --no-progress --no-interaction;

FROM install-prod as prod
EXPOSE 9000
USER www-data
ENTRYPOINT [".docker/prod.entrypoint.sh"]


FROM nginx:stable-alpine as nginx

COPY --link --from=install-prod  /var/www/html/.docker/nginx.conf /etc/nginx/conf.d/default.conf
COPY --link --from=install-prod  /var/www/html/public /var/www/html/public
