<?php

namespace App\Controller;

use App\Services\User\UserServiceInterface;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use Symfony\Component\HttpFoundation\Cookie;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * @Rest\Route("/user")
 */
class UserApiController extends AbstractFOSRestController
{

    private $userService;
    public function __construct(UserServiceInterface $userService)
    {
        $this->userService = $userService;
    }

    /**
     * @Rest\Post("/" , name="user_create" )
     */
    public function create(Request $request)
    {
        $res = $this->userService->register($request);
        if ($res['status'] === Response::HTTP_CREATED) {
            $view = $this->view($res["data"], Response::HTTP_CREATED, []);
        } else {
            $view = $this->view($res["data"], $res['status'], []);
        }
        return $this->handleView($view);
    }

    /**
     * @Rest\Get("/usernames" , name="get_usernames" )
     */
    public function getUsernames()
    {
        $res = $this->userService->getUsernames();
        if ($res['status'] === Response::HTTP_CREATED) {
            $view = $this->view($res["data"], Response::HTTP_CREATED, []);
        } else {
            $view = $this->view($res["data"], $res['status'], []);
        }
        return $this->handleView($view);
    }

    /**
     * @Rest\Delete("/{id}" , name="user_delete" )
     */
    public function delete(int $id)
    {
        $res = $this->userService->delete($id);
        if ($res['status'] === Response::HTTP_OK) {
            $view = $this->view($res["data"], Response::HTTP_OK, []);
        } else {
            $view = $this->view($res["data"], $res['status'], []);
        }
        return $this->handleView($view);
    }

    /**
     * @Rest\Get("/{id}" , name="user" , requirements={"id"="\d+"})
     */
    public function getUserById(int $id)
    {
        $res = $this->userService->getUserById($id);
        if ($res['status'] === Response::HTTP_OK) {
            $view = $this->view($res["data"], Response::HTTP_OK, []);
        } else {
            $view = $this->view($res["data"], $res['status'], []);
        }
        return $this->handleView($view);
    }

    /**
     * @Rest\Post("/{id}" , name="user_update" )
     */
    public function update(Request $request, int $id)
    {
        $res = $this->userService->update($request, $id);
        if ($res['status'] === Response::HTTP_OK) {
            $view = $this->view($res["data"], Response::HTTP_OK, []);
        } else {
            $view = $this->view($res["data"], $res['status'], []);
        }
        return $this->handleView($view);
    }

    /**
     * @Rest\Get("/logout" , name="logout_user")
     */
    public function logout(Request $request)
    {
        //$this->userService->signOut();
        $response = new Response();
        $response->headers->setCookie(
            new Cookie(
                'BEARER',
                '',
                (new \Datetime())->add(new \DateInterval('PT' . 0 . 'S')),
                '/',
                null,
                true,
                true,
                false,
                "None"
            )
        );
        $response->headers->setCookie(
            new Cookie(
                'refresh_token',
                '',
                (new \Datetime())->add(new \DateInterval('PT' . 0 . 'S')),
                '/',
                null,
                true,
                true,
                false,
                "None"
            )
        );
        return $response;
    }


    /**
     * @Rest\Post("/do/action" , name="user_do_action" )
     */
    public function doAction(Request $request)
    {
        $res = $this->userService->doAction($request);
        if ($res['status'] === Response::HTTP_OK) {
            $view = $this->view($res["data"], Response::HTTP_OK, []);
        } else {
            $view = $this->view($res["data"], $res['status'], []);
        }
        return $this->handleView($view);
    }
}
