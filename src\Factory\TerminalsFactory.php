<?php

namespace App\Factory;

use App\Entity\Terminals;
use App\Repository\TerminalsRepository;
use Zen<PERSON><PERSON>ck\Foundry\ModelFactory;
use Zenst<PERSON>ck\Foundry\Proxy;
use Zenst<PERSON>ck\Foundry\RepositoryProxy;

/**
 * @extends ModelFactory<Terminals>
 *
 * @method        Terminals|Proxy create(array|callable $attributes = [])
 * @method static Terminals|Proxy createOne(array $attributes = [])
 * @method static Terminals|Proxy find(object|array|mixed $criteria)
 * @method static Terminals|Proxy findOrCreate(array $attributes)
 * @method static Terminals|Proxy first(string $sortedField = 'id')
 * @method static Terminals|Proxy last(string $sortedField = 'id')
 * @method static Terminals|Proxy random(array $attributes = [])
 * @method static Terminals|Proxy randomOrCreate(array $attributes = [])
 * @method static TerminalsRepository|RepositoryProxy repository()
 * @method static Terminals[]|Proxy[] all()
 * @method static Terminals[]|Proxy[] createMany(int $number, array|callable $attributes = [])
 * @method static Terminals[]|Proxy[] createSequence(iterable|callable $sequence)
 * @method static Terminals[]|Proxy[] findBy(array $attributes)
 * @method static Terminals[]|Proxy[] randomRange(int $min, int $max, array $attributes = [])
 * @method static Terminals[]|Proxy[] randomSet(int $number, array $attributes = [])
 */
final class TerminalsFactory extends ModelFactory
{
    /**
     * @see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#factories-as-services
     *
     * @todo inject services if required
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#model-factories
     *
     * @todo add your default values here
     */
    protected function getDefaults(): array
    {
        return [
            'apiKey' => 'testKey',
            'isOnline' => true,
            'status' => 'active',
        ];
    }

    /**
     * @see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#initialization
     */
    protected function initialize(): self
    {
        return $this
            // ->afterInstantiate(function(Terminals $terminals): void {})
        ;
    }

    protected static function getClass(): string
    {
        return Terminals::class;
    }
}
