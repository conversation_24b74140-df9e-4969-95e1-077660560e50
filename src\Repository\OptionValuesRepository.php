<?php

namespace App\Repository;

use App\Entity\OptionValues;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<OptionValues>
 *
 * @method OptionValues|null find($id, $lockMode = null, $lockVersion = null)
 * @method OptionValues|null findOneBy(array $criteria, array $orderBy = null)
 * @method OptionValues[]    findAll()
 * @method OptionValues[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class OptionValuesRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, OptionValues::class);
    }

//    /**
//     * @return OptionValues[] Returns an array of OptionValues objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('o')
//            ->andWhere('o.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('o.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?OptionValues
//    {
//        return $this->createQueryBuilder('o')
//            ->andWhere('o.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }
}
