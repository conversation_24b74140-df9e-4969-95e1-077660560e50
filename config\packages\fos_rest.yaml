# Read the documentation: https://symfony.com/doc/master/bundles/FOSRestBundle/index.html
fos_rest:
  param_fetcher_listener: true
  #    allowed_methods_listener:  true
  #    routing_loader: true
  #    view:
  #        view_response_listener:  true

  #    exception:
  ##        enabled: true
  ##        exception_controller: 'App\Controller\ExceptionController::showAction'
  #        codes:
  #            'Symfony\Component\Routing\Exception\ResourceNotFoundException': 404
  #            'Symfony\Component\HttpKernel\Exception\UnsupportedMediaTypeHttpException': 404
  #            'Doctrine\ORM\OptimisticLockException': HTTP_CONFLICT
  #            'Symfony\Component\HttpKernel\Exception\BadRequestHttpException': 404
  #            'App\Exception\MyException': 403
  #        messages:
  #            'Symfony\Component\Routing\Exception\ResourceNotFoundException': true

  service:
    view_handler: app.view_handler

  format_listener:
    rules:
      - {
          path: "^/api",
          priorities: ["json", "html"],
          fallback_format: json,
          prefer_extension: true,
        }
      - {
          path: "^/v1",
          priorities: ["json", "html"],
          fallback_format: json,
          prefer_extension: true,
        }
