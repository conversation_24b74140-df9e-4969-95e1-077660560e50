<?php

namespace App\Entity;

use App\Repository\OptionValuesRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\DBAL\Types\Types;

#[ORM\Entity(repositoryClass: OptionValuesRepository::class)]
class OptionValues
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $image = null;

    #[ORM\Column]
    private ?float $price = null;

    #[ORM\Column(length: 255)]
    private ?string $name = null;


    #[ORM\ManyToMany(targetEntity: Options::class, mappedBy: 'defaultOptionsValues')]
    private Collection $defaultIn;

    #[ORM\ManyToMany(targetEntity: Options::class, mappedBy: 'optionValues')]
    private Collection $options;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $externalId = null;

    public function __construct()
    {
        $this->defaultIn = new ArrayCollection();
        $this->options = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getImage(): ?string
    {
        return $this->image;
    }

    public function setImage(string $image): static
    {
        $this->image = $image;

        return $this;
    }

    public function getPrice(): ?float
    {
        return $this->price;
    }

    public function setPrice(float $price): static
    {
        $this->price = $price;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;

        return $this;
    }

    /**
     * @return Collection<int, Options>
     */
    public function getDefaultIn(): Collection
    {
        return $this->defaultIn;
    }

    public function addDefaultIn(Options $defaultIn): static
    {
        if (!$this->defaultIn->contains($defaultIn)) {
            $this->defaultIn->add($defaultIn);
            $defaultIn->addDefaultOptionsValue($this);
        }

        return $this;
    }

    public function removeDefaultIn(Options $defaultIn): static
    {
        if ($this->defaultIn->removeElement($defaultIn)) {
            $defaultIn->removeDefaultOptionsValue($this);
        }

        return $this;
    }

    public function toArray()
    {
        return [
            'id' => $this->getId(),
            'name' => $this->getName(),
            'price' => $this->getPrice(),
            'image' => $this->getImage(),
        ];
    }

    /**
     * @return Collection<int, Options>
     */
    public function getOptions(): Collection
    {
        return $this->options;
    }

    public function addOption(Options $option): static
    {
        if (!$this->options->contains($option)) {
            $this->options->add($option);
            $option->addOptionValue($this);
        }

        return $this;
    }

    public function removeOption(Options $option): static
    {
        if ($this->options->removeElement($option)) {
            $option->removeOptionValue($this);
        }

        return $this;
    }

    public function getExternalId(): ?string
    {
        return $this->externalId;
    }

    public function setExternalId(string $externalId): static
    {
        $this->externalId = $externalId;

        return $this;
    }
}
