<?php

namespace App\Entity;

use App\Repository\OptionsRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: OptionsRepository::class)]
class Options
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    private ?string $name = null;

    #[ORM\Column]
    private ?bool $isRequired = null;

    #[ORM\Column]
    private ?bool $isMultiple = null;


    #[ORM\ManyToOne(inversedBy: 'options', cascade: ['persist'])]
    #[ORM\JoinColumn(nullable: false)]
    private ?Products $product = null;

    #[ORM\ManyToMany(targetEntity: OptionValues::class)]
    #[ORM\JoinTable(name: "options_default_option_value")]
    #[ORM\JoinColumn(name: "option_id", referencedColumnName: "id")]
    #[ORM\InverseJoinColumn(name: "option_value_id", referencedColumnName: "id")]
    private Collection $defaultOptionsValues;

    #[ORM\Column(nullable: true)]
    private ?bool $hasQuantity = null;

    #[ORM\ManyToMany(targetEntity: OptionValues::class)]
    #[ORM\JoinTable(name: "options_values")]
    #[ORM\JoinColumn(name: "option_id", referencedColumnName: "id")]
    #[ORM\InverseJoinColumn(name: "option_value_id", referencedColumnName: "id")]
    private Collection $optionValues;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $externalId = null;

    public function __construct()
    {
        $this->defaultOptionsValues = new ArrayCollection();
        $this->optionValues = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;

        return $this;
    }

    public function isIsRequired(): ?bool
    {
        return $this->isRequired;
    }

    public function setIsRequired(bool $isRequired): static
    {
        $this->isRequired = $isRequired;

        return $this;
    }

    public function isIsMultiple(): ?bool
    {
        return $this->isMultiple;
    }

    public function setIsMultiple(bool $isMultiple): static
    {
        $this->isMultiple = $isMultiple;

        return $this;
    }

    public function getProduct(): ?Products
    {
        return $this->product;
    }

    public function setProduct(?Products $product): static
    {
        $this->product = $product;

        return $this;
    }

    /**
     * @return Collection<int, OptionValues>
     */
    public function getDefaultOptionsValues(): Collection
    {
        return $this->defaultOptionsValues;
    }

    public function addDefaultOptionsValue(OptionValues $defaultOptionsValue): static
    {
        if (!$this->defaultOptionsValues->contains($defaultOptionsValue)) {
            $this->defaultOptionsValues->add($defaultOptionsValue);
        }

        return $this;
    }

    public function removeDefaultOptionsValue(OptionValues $defaultOptionsValue): static
    {
        $this->defaultOptionsValues->removeElement($defaultOptionsValue);

        return $this;
    }

    public function toArray(): array
    {
        return [
            'id' => $this->getId(),
            'name' => $this->getName(),
            'isRequired' => $this->isIsRequired(),
            'isMultiple' => $this->isIsMultiple(),
            'hasQuantity' => $this->isHasQuantity(),
            'defaultOptionsValues' => $this->getDefaultOptionsValues()->map(fn(OptionValues $optionValue) => $optionValue->toArray()),
            'optionValues' => $this->getOptionValues()->map(fn(OptionValues $optionValue) => $optionValue->toArray()),
        ];
    }

    public function __toString(): string
    {
        return $this->getName();
    }

    public function isHasQuantity(): ?bool
    {
        return $this->hasQuantity;
    }

    public function setHasQuantity(?bool $hasQuantity): static
    {
        $this->hasQuantity = $hasQuantity;

        return $this;
    }

    /**
     * @return Collection<int, OptionValues>
     */
    public function getOptionValues(): Collection
    {
        return $this->optionValues;
    }

    public function addOptionValue(OptionValues $optionValue): static
    {
        if (!$this->optionValues->contains($optionValue)) {
            $this->optionValues->add($optionValue);
        }

        return $this;
    }

    public function removeOptionValue(OptionValues $optionValue): static
    {
        $this->optionValues->removeElement($optionValue);

        return $this;
    }

    public function getExternalId(): ?string
    {
        return $this->externalId;
    }

    public function setExternalId(string $externalId): static
    {
        $this->externalId = $externalId;

        return $this;
    }
}
