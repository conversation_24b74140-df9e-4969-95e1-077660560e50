<?php

namespace App\Services\Media;

use App\Entity\Media;
use Doctrine\ORM\EntityManagerInterface;
use App\Services\Media\MediaServiceInterface;
use App\Utils\Uploader;
use DateTime;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Security\Core\Security;
use Symfony\Component\HttpFoundation\Response;

class MediaService implements MediaServiceInterface
{

    private EntityManagerInterface $em;
    private Security $security;

    public function __construct(EntityManagerInterface $entityManager, Security $security)
    {
        $this->em = $entityManager;
        $this->security = $security;
    }

    public function getAllMedia(): array
    {
        $allMedia = $this->em->getRepository(Media::class)->findAll();
        $result = [];

        foreach ($allMedia as $media) {
            $result[] = $media->toArray();
        }
        return [
            'status' => Response::HTTP_OK,
            'data' => $result
        ];
    }

    public function getMediaById(int $id): array
    {
        $media = $this->em->getRepository(Media::class)->find($id);
        if (!$media) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'message' => 'Media not found'
            ];
        }
        return [
            'status' => Response::HTTP_OK,
            'data' => $media->toArray()
        ];
    }

    public function addMedia(Request $request): array
    {
        if (!$request->get('type')) {
            return [
                'status' => Response::HTTP_BAD_REQUEST,
                'data' => 'Type is required'
            ];
        }

        $media = new Media();
        $media->setType($request->get('type'));

        $file = $request->files->get('link');
        $link = $request->get('link');

        if (!$file && !$link) {
            return [
                'status' => Response::HTTP_BAD_REQUEST,
                'data' => 'Link is required'
            ];
        }

        if ($file) {
            if (!$file->isValid()) {
                return [
                    'status' => Response::HTTP_BAD_REQUEST,
                    'data' => 'File is not valid'
                ];
            }

            $fileLink = null;
            $mimeType = $file->getMimeType();

            if (strpos($mimeType, 'image/') === 0) {
                $fileLink = Uploader::uploadImageToWebp($file, 'media');
            } elseif (strpos($mimeType, 'video/') === 0) {
                $fileLink = Uploader::uploadVideo($file, 'media');
            } else {
                $fileLink = Uploader::uploadFile($file, 'media');
            }

            if (!$fileLink) {
                return [
                    'status' => Response::HTTP_BAD_REQUEST,
                    'message' => 'File upload failed'
                ];
            }

            $media->setLink($fileLink);
        } else {
            $media->setLink($link);
        }

        try {
            $this->em->persist($media);
            $this->em->flush();
        } catch (\Exception $e) {
            return [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'message' => 'An error occurred while saving the media'
            ];
        }

        return [
            'status' => Response::HTTP_OK,
            'data' => $media->toArray()
        ];
    }

    public function updateMedia(Request $request, int $id): array
    {
        $media = $this->em->getRepository(Media::class)->find($id);
        if (!$media) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'message' => 'Media not found'
            ];
        }
        $oldFile = $media->getLink();
        $media->setType($request->get('type'));
        $file = $request->files->get('link');
        if (!$file && !$request->get('link')) {
            return [
                'status' => Response::HTTP_BAD_REQUEST,
                'message' => 'Link is required'
            ];
        }
        if ($file) {
            $fileLink = null;
            // check if file a video or image
            $mimeType = mime_content_type($file);
            dd($mimeType);
            if (strpos($mimeType, 'image/') === 0) {
                $fileLink = Uploader::uploadImageToWebp($file, 'media');
            } elseif (strpos($mimeType, 'video/') === 0) {
                $fileLink = Uploader::uploadVideo($file, 'media');
            } else {
                $fileLink = Uploader::uploadFile($file, 'media');
                // File is neither an image nor a video
            }
            //$fileLink = Uploader::uploadFile($file, 'media');
            if (!$fileLink) {
                return [
                    'status' => Response::HTTP_BAD_REQUEST,
                    'message' => 'File upload failed'
                ];
            }
            $media->setLink($fileLink);
        } else {
            $media->setLink($request->get('link'));
        }
        Uploader::deleteFile($oldFile);
        $this->em->flush();
        return [
            'status' => Response::HTTP_OK,
            'data' => $media->toArray()
        ];
    }

    public function deleteMedia(int $id): array
    {
        $media = $this->em->getRepository(Media::class)->find($id);
        if (!$media) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'data' => 'Media not found'
            ];
        }
        Uploader::deleteFile($media->getLink());
        $this->em->remove($media);
        $this->em->flush();

        return [
            'status' => Response::HTTP_OK,
            'data' => ['Media deleted successfully']
        ];
    }

    public function doAction(Request $request): array
    {
        $action = $request->get('action');
        $id = $request->get('id');
        switch ($action) {
            default:
                return [
                    'status' => Response::HTTP_BAD_REQUEST,
                    'message' => 'Invalid action'
                ];
        }
        return [
            'status' => Response::HTTP_BAD_REQUEST,
            'message' => 'Action not found'
        ];
    }

    public function getMediaByType(string $type): array
    {
        $media = $this->em->getRepository(Media::class)->findBy(['type' => $type]);
        $result = [];
        foreach ($media as $m) {
            $result[] = $m->toArray();
        }
        return [
            'status' => Response::HTTP_OK,
            'data' => $result
        ];
    }
}
