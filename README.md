
# Symfony Rest API Start-Kit

This is a start kit for a Symfony Restful API project 




## Tech Stack

**Server:** IIS , Symfony, Rest


## Run Locally

Clone the project

```bash
  git clone https://link-to-project
```

Go to the project directory

```bash
  cd RestfulSymfony
```

Install dependencies

```bash
  composer update
  composer install
```

Start the server

```bash
  symfony server:start
```



### JTTP Coherent output formats
[JTTP](https://github.com/demartis/jttp) is the default protocol 

General JTTP output format:

```json
{
    "status": "success|error",
    "code": "HTTP status code",
    "message": "HTTP status message",
    "data|error": {
        "your data": "data or error field only in case of success or error"
    }
}
```

## Acknowledgements

 - [JMSSerializerBundle](https://github.com/schmittjoh/JMSSerializerBundle)
 - [The Symfony MakerBundle](https://symfony.com/bundles/SymfonyMakerBundle/current/index.html)
 - [FOSRestBundle](https://github.com/FriendsOfSymfony/FOSRestBundle)
 - [LexikJWTAuthenticationBundle](https://github.com/lexik/LexikJWTAuthenticationBundle)
 - [JWTRefreshTokenBundle](https://github.com/markitosgv/JWTRefreshTokenBundle)
 - [Symfony JsonRequest Bundle](https://github.com/symfony-bundles/json-request-bundle)
 - [SensioFrameworkExtraBundle](https://symfony.com/bundles/SensioFrameworkExtraBundle/current/index.html)
 - [Monolog Bundle](https://symfony.com/doc/current/logging.html)
 - [Gentle-force bundle](https://github.com/mariusbalcytis/gentle-force-bundle)
 add Others here ........
 - [Awesome Readme](https://awesomeopensource.com/project/elangosundar/awesome-README-templates)
 
## Demo

Utilisateur : <EMAIL>
Mot de passe : 123456789

Administrateur : <EMAIL>
Mo de passe : 123456789

## Documentation

L'utilisateur se connecte sur la page [login](https://127.0.0.1:8000/login)
se retrouve sur la page [upload](https://127.0.0.1:8000/upload) où il peut trouver tous ses projets, leurs détails et la liste des fichiers à upload
Le bouton upload emmenne à la page d'ajout des fichiers où on peut selectionner les fichiers et les types.
Les fichiers enregistrés sous le dossier public/uploads/doc

L'administrateur se connecte de la meme page, avec la structure précédente, quand il va ajouter un client il retrouve deux nouveaux champs que j'ai ajouté : Username & Password pour que l'utilisateur puisse se connecter avec ces informations


Systéme d'authentification qui ne permet pas au client de voir les informations des autres clients dans la page [Clients](https://127.0.0.1:8000/clients/)
et [Projects](https://127.0.0.1:8000/projets/)

## Appendix

Any additional information goes here


## Authors

- [@TuniSolutions](https://tunisolutions.com)


![Logo](http://tunisolutions.com/wp-content/uploads/2022/03/logoTuniSolutions.png)

