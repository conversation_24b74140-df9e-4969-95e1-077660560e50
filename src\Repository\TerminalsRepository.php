<?php

namespace App\Repository;

use App\Entity\Terminals;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Terminals>
 *
 * @method Terminals|null find($id, $lockMode = null, $lockVersion = null)
 * @method Terminals|null findOneBy(array $criteria, array $orderBy = null)
 * @method Terminals[]    findAll()
 * @method Terminals[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class TerminalsRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Terminals::class);
    }

//    /**
//     * @return Terminals[] Returns an array of Terminals objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('t')
//            ->andWhere('t.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('t.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?Terminals
//    {
//        return $this->createQueryBuilder('t')
//            ->andWhere('t.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }
}
