security:
    enable_authenticator_manager: true
    # https://symfony.com/doc/current/security.html#registering-the-user-hashing-passwords
    password_hashers:
        Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface: "auto"
        App\Entity\User:
            algorithm: auto

    # https://symfony.com/doc/current/security.html#loading-the-user-the-user-provider
    providers:
        # used to reload user from session & other features (e.g. switch_user)
        app_user_provider:
            entity:
                class: App\Entity\User
                property: username
    firewalls:
        login:
            pattern: ^/v1/login
            stateless: true
            json_login:
                check_path: /v1/login_check
                success_handler: lexik_jwt_authentication.handler.authentication_success
                failure_handler: lexik_jwt_authentication.handler.authentication_failure

        api:
            pattern: ^/v1
            stateless: true
            entry_point: jwt
            jwt: ~
            refresh_jwt:
                check_path: /v1/token/refresh

        dev:
            pattern: ^/(_(profiler|wdt)|css|images|js)/
            security: false
        main:
            lazy: true
            provider: app_user_provider

            # activate different ways to authenticate
            # https://symfony.com/doc/current/security.html#the-firewall

            # https://symfony.com/doc/current/security/impersonating_user.html
            # switch_user: true

    # Easy way to control access for large sections of your site
    # Note: Only the *first* access control that matches will be used
    access_control:
        - { path: ^/v1/(login|token/refresh|user/signup|user/logout), roles: PUBLIC_ACCESS }
        - { path: ^/v1/terminal/ping, roles: PUBLIC_ACCESS }
        - { path: ^/v1/user/usernames, methods: [Get] , roles: PUBLIC_ACCESS }
        - { path: ^/v1/category, methods: [Get] , roles: PUBLIC_ACCESS }
        - { path: ^/v1/media/type/*, methods: [Get] , roles: PUBLIC_ACCESS }
        - { path: ^/v1/media/locales/*, methods: [Get] , roles: PUBLIC_ACCESS }
        - { path: ^/v1/product, methods: [Get , Post] , roles: PUBLIC_ACCESS }
        - { path: ^/v1/order, methods: [Get , Post] , roles: PUBLIC_ACCESS }
        - { path: ^/v1/setting/get_resturant_settings, methods: [Get] , roles: PUBLIC_ACCESS }
        - { path: ^/v1/payment_methodes/active, methods: [Get] , roles: PUBLIC_ACCESS }
        - { path: ^/v1, roles: IS_AUTHENTICATED_FULLY }
#when@test:
#    security:
#        password_hashers:
# By default, password hashers are resource intensive and take time. This is
# important to generate secure password hashes. In tests however, secure hashes
# are not important, waste resources and increase test times. The following
# reduces the work factor to the lowest possible values.
#            ? Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface
#            : algorithm: auto
#              cost: 4 # Lowest possible value for bcrypt
#              time_cost: 3 # Lowest possible value for argon
#              memory_cost: 10 # Lowest possible value for argon
