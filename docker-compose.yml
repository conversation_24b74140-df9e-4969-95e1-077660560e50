version: '3'

services:
  php:
    container_name: BE-Terminal-Local-php
    build:
      context: .
      target: dev
    environment:
      - APP_ENV=dev
      - APP_SECRET=51adcf5c0270e41eb4edfd922a1409f4
      - JWT_SECRET_KEY=%kernel.project_dir%/config/jwt/private.pem
      - JWT_PUBLIC_KEY=%kernel.project_dir%/config/jwt/public.pem
      - JWT_PASSPHRASE=2024be7b2352ca4181ab6a45a743f5420
      - DATABASE_URL=****************************************/app
    volumes:
      - ./:/var/www/html
    depends_on:
      - database
    networks:
      - BE-Terminal-Local-Network

  nginx:
    container_name: BE-Terminal-Local-nginx
    image: nginx:alpine
    volumes:
      - ./:/var/www/html
      - ./.docker/nginx.conf:/etc/nginx/conf.d/default.conf
    ports:
      - "80:80"
    depends_on:
      - php
    networks:
      - BE-Terminal-Local-Network

  database:
    container_name: BE-Terminal-Local-postgres
    image: postgres:16-alpine
    ports:
      - "5432:5432"
    environment:
      POSTGRES_DB: app
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
    volumes:
      - db-data:/var/lib/postgresql/data:rw
    networks:
      - BE-Terminal-Local-Network

volumes:
  db-data:

networks:
  BE-Terminal-Local-Network: