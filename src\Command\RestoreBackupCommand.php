<?php

namespace App\Command;

use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use ZipArchive;
use Symfony\Component\Filesystem\Filesystem;

#[AsCommand(name: 'app:restore-backup')]
class RestoreBackupCommand extends Command
{
    private string $backupDir;

    public function __construct()
    {
        parent::__construct();
        $this->backupDir = __DIR__ . '/../../public/backups/';
    }

    protected function configure(): void
    {
        $this
            ->addArgument('backupFile', InputArgument::REQUIRED, 'The ZIP backup file to restore')
            ->setDescription('Restores a backup from the specified ZIP file');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $backupFile = $input->getArgument('backupFile');
        $zipPath = $this->backupDir . $backupFile;

        if (!file_exists($zipPath)) {
            $output->writeln("<error>Backup file not found: $zipPath</error>");
            return Command::FAILURE;
        }

        $extractDir = sys_get_temp_dir() . '/backup_restore_' . uniqid();
        mkdir($extractDir);

        $zip = new ZipArchive();
        if ($zip->open($zipPath) !== true) {
            $output->writeln("<error>Failed to open ZIP archive.</error>");
            return Command::FAILURE;
        }

        $zip->extractTo($extractDir);
        $zip->close();

        $fs = new Filesystem();

        // Restore DB
        $dbFiles = glob("$extractDir/data_*.db");
        if (empty($dbFiles)) {
            $output->writeln("<error>No database file found in the backup.</error>");
            return Command::FAILURE;
        }

        $fs->copy($dbFiles[0], __DIR__ . '/../../var/data.db', true);
        $output->writeln("<info>Database restored.</info>");

        // Restore images
        $sourceImages = $extractDir . '/images';
        $destImages = __DIR__ . '/../../public/images';
        if (is_dir($sourceImages)) {
            if (is_dir($destImages)) {
                $fs->remove($destImages); // delete old images
            }
            $fs->mirror($sourceImages, $destImages);
            $output->writeln("<info>Images restored (old data deleted).</info>");
        }

        // Restore videos
        $sourceVideos = $extractDir . '/videos';
        $destVideos = __DIR__ . '/../../public/videos';
        if (is_dir($sourceVideos)) {
            if (is_dir($destVideos)) {
                $fs->remove($destVideos); // delete old videos
            }
            $fs->mirror($sourceVideos, $destVideos);
            $output->writeln("<info>Videos restored (old data deleted).</info>");
        }

        // Clean up
        $fs->remove($extractDir);

        $output->writeln("<info>Backup restored successfully from: $backupFile</info>");
        return Command::SUCCESS;
    }
}
