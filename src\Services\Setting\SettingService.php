<?php

namespace App\Services\Setting;

use App\Entity\Settings;
use App\Services\Setting\SettingServiceInterface;
use App\Utils\Uploader;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Security\Core\Security;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Process\Process;

class SettingService implements SettingServiceInterface
{

    private $em;
    private $security;
    private string $projectDir;

    public function __construct(
        EntityManagerInterface $entityManager,
        Security               $security,
        string                 $projectDir
    ) {
        $this->em = $entityManager;
        $this->security = $security;
        $this->projectDir = $projectDir;
    }

    public function update(Request $request)
    {
        $type = $request->get('type');
        if (!$type) {
            return [
                'status' => Response::HTTP_BAD_REQUEST,
                'data' => 'type is required'
            ];
        }
        $data = $request->get('data');
        if (!$data) {
            return [
                'status' => Response::HTTP_BAD_REQUEST,
                'data' => 'data is required'
            ];
        }

        if ($type === 'add_new_key') {
            return $this->addNewKey($data);
        }

        if ($type === 'language') {
            return $this->updateLanguage($data);
        }

        if ($type === 'modify_settings') {
            return $this->updateRestorantSettings($request);
        }
        if ($type === 'backup_database') {
            return $this->backupDatabase();
        }
        if ($type === 'delete_backup') {
            return $this->deleteBackup($data);
        }
        if ($type === 'clear_database') {
            return $this->clearDatabase($data);
        }
        if ($type === 'restore_backup') {
            return $this->restoreBackup($data);
        }
        if ($type === 'import_database') {
            return $this->importDatabase($request);
        }
        return [
            'status' => Response::HTTP_BAD_REQUEST,
            'data' => 'setting not found'
        ];

        /* $setting = $this->em->getRepository(Settings::class)->findOneBy(['name' => $type]);
        if (!$setting) {
            
        }

        $setting->setAttributs($data);
        $this->em->flush(); */

        return [
            'status' => Response::HTTP_OK,
            'data' => $setting->getAttributs()
        ];
    }


    public function backupDatabase()
    {
        $consolePath = realpath(getcwd() . '/../bin/console');
        $process = new Process(['php', $consolePath, 'app:backup-sqlite']);
        $process->run();

        if (!$process->isSuccessful()) {
            return [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'data' => 'Database backup failed: ' . $process->getErrorOutput()
            ];
        }

        return [
            'status' => Response::HTTP_OK,
            'data' => ['Database backup created successfully at ' . $this->projectDir . '/public/backups']
        ];
    }

    public function restoreBackup(array $data)
    {
        $consolePath = realpath(getcwd() . '/../bin/console');
        $process = new Process(['php', $consolePath, 'app:restore-backup', $data['backup']]);
        $process->run();

        if (!$process->isSuccessful()) {
            return [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'data' => 'Database restore failed: ' . $process->getErrorOutput()
            ];
        }

        return [
            'status' => Response::HTTP_OK,
            'data' => ['Database restored successfully from ' . $data['backup']]
        ];
    }

    public function deleteBackup(array $data)
    {
        $fileNameToDelete = $data['backup'];
        $backupDir = $this->projectDir . '/public/backups';
        if (!is_dir($backupDir)) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'data' => 'No backups found'
            ];
        }
        $filePath = $backupDir . '/' . $fileNameToDelete;
        if (!file_exists($filePath)) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'data' => 'Backup file not found'
            ];
        }
        unlink($filePath);
        return [
            'status' => Response::HTTP_OK,
            'data' => ['Backup file deleted successfully']
        ];
    }

    public function importDatabase(Request $request)
    {
        $file = $request->files->get('file');
        if (!$file) {
            return [
                'status' => Response::HTTP_BAD_REQUEST,
                'data' => 'file is required'
            ];
        }
        $consolePath = realpath(getcwd() . '/../bin/console');
        $process = new Process(['php', $consolePath, 'app:backup-sqlite']);
        $process->run();

        $importPath = $this->projectDir . '/public/backups';
        if (!is_dir($importPath)) mkdir($importPath);
        $file->move($importPath, 'imported-' . $file->getClientOriginalName());

        $process = new Process(['php', $consolePath, 'app:restore-backup', 'imported-' . $file->getClientOriginalName()]);
        $process->run();

        return [
            'status' => Response::HTTP_OK,
            'data' => ['Database imported successfully']
        ];
    }

    public function clearDatabase(array $data)
    {
        $tableToClear = $data['clear'] ?? null;

        if (!$tableToClear) {
            return [
                'status' => Response::HTTP_BAD_REQUEST,
                'data' => 'Confirmation is required to clear the database'
            ];
        }

        $connection = $this->em->getConnection();

        try {
            if ($tableToClear === 'all') {
                $connection->executeStatement('DELETE FROM order_item');
                $connection->executeStatement('DELETE FROM "order"');
                $connection->executeStatement('DELETE FROM "products"');
                $connection->executeStatement('DELETE FROM "products_tags"');
                $connection->executeStatement('DELETE FROM "products_products"');
                $connection->executeStatement('DELETE FROM "options"');
                $connection->executeStatement('DELETE FROM "options_values"');
                $connection->executeStatement('DELETE FROM "options_default_option_value"');
                $connection->executeStatement('DELETE FROM "category"');
                $connection->executeStatement('UPDATE "sqlite_sequence" SET "seq" = 0 WHERE "name" = "order_item"');
                $connection->executeStatement('UPDATE "sqlite_sequence" SET "seq" = 0 WHERE "name" = "order"');
                $connection->executeStatement('UPDATE "sqlite_sequence" SET "seq" = 0 WHERE "name" = "products"');
                $connection->executeStatement('UPDATE "sqlite_sequence" SET "seq" = 0 WHERE "name" = "products_tags"');
                $connection->executeStatement('UPDATE "sqlite_sequence" SET "seq" = 0 WHERE "name" = "products_products"');
                $connection->executeStatement('UPDATE "sqlite_sequence" SET "seq" = 0 WHERE "name" = "options"');
                $connection->executeStatement('UPDATE "sqlite_sequence" SET "seq" = 0 WHERE "name" = "options_values"');
                $connection->executeStatement('UPDATE "sqlite_sequence" SET "seq" = 0 WHERE "name" = "options_default_option_value"');
                $connection->executeStatement('UPDATE "sqlite_sequence" SET "seq" = 0 WHERE "name" = "category"');
            } elseif ($tableToClear === 'orders') {
                $connection->executeStatement('DELETE FROM "order_item"');
                $connection->executeStatement('DELETE FROM "order"');
                $connection->executeStatement('UPDATE "sqlite_sequence" SET "seq" = 0 WHERE "name" = "order_item"');
                $connection->executeStatement('UPDATE "sqlite_sequence" SET "seq" = 0 WHERE "name" = "order"');
            } elseif ($tableToClear === 'products') {
                $connection->executeStatement('DELETE FROM "products"');
                $connection->executeStatement('DELETE FROM "products_tags"');
                $connection->executeStatement('DELETE FROM "products_products"');
                $connection->executeStatement('DELETE FROM "options"');
                $connection->executeStatement('DELETE FROM "options_values"');
                $connection->executeStatement('DELETE FROM "options_default_option_value"');
                $connection->executeStatement('DELETE FROM "category"');
                $connection->executeStatement('UPDATE "sqlite_sequence" SET "seq" = 0 WHERE "name" = "products"');
                $connection->executeStatement('UPDATE "sqlite_sequence" SET "seq" = 0 WHERE "name" = "products_tags"');
                $connection->executeStatement('UPDATE "sqlite_sequence" SET "seq" = 0 WHERE "name" = "products_products"');
                $connection->executeStatement('UPDATE "sqlite_sequence" SET "seq" = 0 WHERE "name" = "options"');
                $connection->executeStatement('UPDATE "sqlite_sequence" SET "seq" = 0 WHERE "name" = "options_values"');
                $connection->executeStatement('UPDATE "sqlite_sequence" SET "seq" = 0 WHERE "name" = "options_default_option_value"');
                $connection->executeStatement('UPDATE "sqlite_sequence" SET "seq" = 0 WHERE "name" = "category"');
            } elseif ($tableToClear === 'orders') {
            } else {
                return [
                    'status' => Response::HTTP_BAD_REQUEST,
                    'data' => 'Invalid table specified for clearing'
                ];
            }
        } catch (\Exception $e) {
            return [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'data' => 'Error clearing database: ' . $e->getMessage()
            ];
        }

        return [
            'status' => Response::HTTP_OK,
            'data' => ['Database cleared successfully']
        ];
    }


    public function getAllBackups()
    {
        $backupDir = $this->projectDir . '/public/backups';
        if (!is_dir($backupDir)) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'data' => 'No backups found'
            ];
        }

        $files = scandir($backupDir);
        $backups = array_filter($files, function ($file) use ($backupDir) {
            return is_file($backupDir . '/' . $file) && pathinfo($file, PATHINFO_EXTENSION) === 'zip';
        });

        return [
            'status' => Response::HTTP_OK,
            'data' => array_values($backups)
        ];
    }

    public function updateRestorantSettings($request)
    {
        $logo = $request->files->get('logo');
        $takeawayImage = $request->files->get('takeaway_image');
        $dineinImage = $request->files->get('dinein_image');
        if ($logo) {
            $imagelink = Uploader::uploadImageToWebp($request->files->get('logo'), 'brand');
            $logoSettings = $this->em->getRepository(Settings::class)->findOneBy(['name' => 'logo']);
            if (!$logoSettings) {
                $logoSettings = new Settings();
                $logoSettings->setName('logo');
                $logoSettings->setAttribute($imagelink);
                $this->em->persist($logoSettings);
            } else {
                $logoSettings->setAttribute($imagelink);
            }
        }
        if ($takeawayImage) {
            $imagelink = Uploader::uploadImageToWebp($request->files->get('takeaway_image'), 'brand');
            $takeawayImageSettings = $this->em->getRepository(Settings::class)->findOneBy(['name' => 'takeaway_image']);
            if (!$takeawayImageSettings) {
                $takeawayImageSettings = new Settings();
                $takeawayImageSettings->setName('takeaway_image');
                $takeawayImageSettings->setAttribute($imagelink);
                $this->em->persist($takeawayImageSettings);
            } else {
                $takeawayImageSettings->setAttribute($imagelink);
            }
        }
        if ($dineinImage) {
            $imagelink = Uploader::uploadImageToWebp($request->files->get('dinein_image'), 'brand');
            $dineinImageSettings = $this->em->getRepository(Settings::class)->findOneBy(['name' => 'dinein_image']);
            if (!$dineinImageSettings) {
                $dineinImageSettings = new Settings();
                $dineinImageSettings->setName('dinein_image');
                $dineinImageSettings->setAttribute($imagelink);
                $this->em->persist($dineinImageSettings);
            } else {
                $dineinImageSettings->setAttribute($imagelink);
            }
        }
        $data = $request->request->all();
        unset($data['type']);
        unset($data['data']);
        $settings = $this->em->getRepository(Settings::class)->findAll();
        foreach ($settings as $setting) {
            if (isset($data[$setting->getName()])) {
                $setting->setAttribute($data[$setting->getName()]);
            }
        }
        $this->em->flush();
        $result = [];
        foreach ($settings as $setting) {
            $result[$setting->getName()] = $setting->getAttribute();
        }

        return [
            'status' => Response::HTTP_OK,
            'data' => $result
        ];
    }

    public function updateLanguage($data)
    {
        $lang = $data['lng'];
        $languageFile = '';
        switch ($lang) {
            case 'en':
                $languageFile = getcwd() . '/locales/en_US.json';
                break;

            case 'fr':
                $languageFile = getcwd() . '/locales/fr_FR.json';
                break;

            case 'ar':
                $languageFile = getcwd() . '/locales/ar_TN.json';
                break;

            default:
                $languageFile = getcwd() . '/locales/en_US.json';
                break;
        }
        $json = file_get_contents($languageFile);
        $language = json_decode($json, true);

        $Changedkeys = $data['keys'];
        foreach ($Changedkeys as $key => $value) {
            $language[$key] = $value;
        }

        file_put_contents($languageFile, json_encode($language));

        return [
            'status' => Response::HTTP_OK,
            'data' => $data
        ];
    }

    public function getRestorantSettings()
    {
        $settings = $this->em->getRepository(Settings::class)->findAll();
        $data = [];
        foreach ($settings as $setting) {
            $data[$setting->getName()] = $setting->getAttribute();
        }
        return [
            'status' => Response::HTTP_OK,
            'data' => $data
        ];
    }

    public function getSetting(String $type)
    {
        switch ($type) {
            case 'language_en':
                return $this->getLanguage('en');

            case 'language_fr':
                return $this->getLanguage('fr');

            case 'language_ar':
                return $this->getLanguage('ar');

            case 'get_resturant_settings':
                return $this->getRestorantSettings();
            case 'get_all_backups':
                return $this->getAllBackups();

            default:
                return [
                    'status' => Response::HTTP_BAD_REQUEST,
                    'data' => 'invalid type'
                ];
        }
    }

    public function addNewKey($data)
    {
        $key = $data['key'];
        $valueAR = $data['value_ar'];
        $valueFR = $data['value_fr'];
        $valueEN = $data['value_en'];

        $arLanguageFile = getcwd() . '/locales/ar_TN.json';
        $frLanguageFile = getcwd() . '/locales/fr_FR.json';
        $enLanguageFile = getcwd() . '/locales/en_US.json';

        $arJson = file_get_contents($arLanguageFile);
        $frJson = file_get_contents($frLanguageFile);
        $enJson = file_get_contents($enLanguageFile);

        $arData = json_decode($arJson, true);
        $frData = json_decode($frJson, true);
        $enData = json_decode($enJson, true);

        $arData[$key] = $valueAR;
        $frData[$key] = $valueFR;
        $enData[$key] = $valueEN;

        file_put_contents($arLanguageFile, json_encode($arData));
        file_put_contents($frLanguageFile, json_encode($frData));
        file_put_contents($enLanguageFile, json_encode($enData));

        return [
            'status' => Response::HTTP_OK,
            'data' => ['done']
        ];
    }

    public function getLanguage(String $lang)
    {
        // get json object from file
        $languageFile = '';
        switch ($lang) {
            case 'en':
                $languageFile = getcwd() . '/locales/en_US.json';
                break;

            case 'fr':
                $languageFile = getcwd() . '/locales/fr_FR.json';
                break;

            case 'ar':
                $languageFile = getcwd() . '/locales/ar_TN.json';
                break;

            default:
                $languageFile = getcwd() . '/locales/en_US.json';
                break;
        }
        $json = file_get_contents($languageFile);
        return [
            'status' => Response::HTTP_OK,
            'data' => json_decode($json, true)
        ];
    }

    public function getAll() {}

    public function doAction(Request $request)
    {
        $action = $request->get('action');
        return match ($action) {
            default => [
                'status' => Response::HTTP_BAD_REQUEST,
                'data' => 'action not found'
            ],
        };
    }
}
