<?php

namespace App\Services\Category;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;

interface CategoryServiceInterface
{
    public function getAllCategories(): array;

    public function getCategoryById(int $id): array;

    public function addCategory(Request $request): array;

    public function updateCategory(Request $request, int $id): array;

    public function deleteCategory(int $id): array;

    public function doAction(Request $request): array;
}
