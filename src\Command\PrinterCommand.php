<?php

namespace App\Command;

use App\Entity\Options;
use App\Entity\OptionValues;
use App\Entity\Order;
use App\Entity\Printer;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Mike42\Escpos\PrintConnectors\NetworkPrintConnector;
use Mike42\Escpos\PrintConnectors\WindowsPrintConnector;
use Mike42\Escpos\Printer as PrinterLib;

class PrinterCommand extends Command
{
    protected static $defaultName = 'order:print';
    private $em;

    public function __construct(EntityManagerInterface $em, HttpClientInterface $httpClient)
    {
        $this->em = $em;
        parent::__construct();
    }
    protected function configure(): void
    {
        $this
            ->addArgument('orderId', null, 'Argument description')
            ->setDescription('Test Printer Command')
            ->setHelp('This command allows you to test the printer');
    }

    /**
     * @throws \Exception
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $orderId = $input->getArgument('orderId');
        $order = $this->em->getRepository(Order::class)->find($orderId);
        if (!$order) {
            $output->writeln('Order Not Found');
            return Command::FAILURE;
        }
        $printers = $this->em->getRepository(Printer::class)->findAll();
        $mainPrinter = $this->em->getRepository(Printer::class)->findOneBy(['isMain' => true]);



        $orderItems = $order->getOrderItems();
        $productsByPrinter = [];
        foreach ($printers as $printer) {
            $productsByPrinter[$printer->getIpAdress()] = [];
        }

        // Group products by category
        foreach ($orderItems as $item) {
            $product = $item->getProduct();
            if ($product->isIsPack()) {
                foreach ($product->getSubProducts() as $subProduct) {
                    $category = $subProduct->getCategory();
                    $productsByPrinter[$category->getPrinter()->getIpAdress()][] = [
                        'product' => $subProduct,
                        'quantity' => $item->getQuantity(),
                        'options' => $subProduct === $product->getMainProduct() ? $item->getOptions() : []
                    ];
                }
            } else {
                $category = $product->getCategory();
                $categoryPrinterIp = $category->getPrinter() ? $category->getPrinter()->getIpAdress() : $mainPrinter->getIpAdress();
                $productsByPrinter[$categoryPrinterIp][] = [
                    'product' => $item->getProduct(),
                    'quantity' => $item->getQuantity(),
                    'options' => $item->getOptions()
                ];
            }
        }


        //clear empty printers
        foreach ($productsByPrinter as $printerIpAddress => $items) {
            if (empty($items)) {
                unset($productsByPrinter[$printerIpAddress]);
            }
        }



        // Print products category-wise
        foreach ($productsByPrinter as $printerIpAddress => $items) {
            //$logger->info("Initiating print job to printer: {$printerIpAddress}");

            try {
                // Connect to the printer - using Windows printer name instead of IP
                //$connector = new WindowsPrintConnector('TM-T20II');
                $connector = new NetworkPrintConnector($printerIpAddress, 9100);
                //$logger->info("Connection established with printer: {$printerIpAddress}");

                $printer = new PrinterLib($connector);

                // Print order header with clear formatting
                $printer->setEmphasis(true);
                $printer->setTextSize(2, 2);
                $printer->text("ORDER #{$orderId}\n");
                $printer->setTextSize(1, 1);
                $printer->setEmphasis(false);
                $printer->text(date('Y-m-d H:i:s') . "\n");
                $printer->text("================================\n\n");

                // Group items for clearer kitchen reading
                foreach ($items as $item) {
                    $product = $item['product'];

                    // Product name and quantity in bold
                    $printer->setEmphasis(true);
                    $printer->text($item['quantity'] . "x " . strtoupper($product->getName()) . "\n");
                    $printer->setEmphasis(false);

                    // Print options with proper indentation
                    if (!empty($item['options'])) {
                        foreach ($item['options'] as $optionValue) {
                            $printer->text("   • " . $optionValue['quantity'] . "x " . $optionValue['name'] . ": " . $optionValue['value'] . "\n");
                        }
                    }
                }

                // Cut the paper
                $printer->cut();

                //$logger->info("Print job completed successfully for printer: {$printerIpAddress}");
            } catch (\Exception $e) {
                // Proper error handling with logging
                //$output->writeln("Print job failed for printer {$printerIpAddress}: {$e->getMessage()}");
                //$this->notifyAdminOfPrinterFailure($printerIpAddress, $e->getMessage());
                continue; // Process next printer
            } finally {
                // Proper resource cleanup
                if (isset($printer)) {
                    $printer->close();
                    //$logger->debug("Printer connection closed: {$printerIpAddress}");
                }
            }
        }
        return Command::SUCCESS;
    }
}
