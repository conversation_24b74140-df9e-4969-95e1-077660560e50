<?php

namespace App\Controller;

use App\Services\Order\OrderServiceInterface;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * @Rest\Route("/order" , name="order_")
 */
class OrderApiContorller extends AbstractFOSRestController
{

    private $orderService;
    public function __construct(OrderServiceInterface $orderService)
    {
        $this->orderService = $orderService;
    }

    /**
     * @Rest\Post("/" , name="create")
     */
    public function create(Request $request): Response
    {
        $res = $this->orderService->create($request);
        $view = $this->view($res["data"], $res['status'], []);
        return $this->handleView($view);
    }

    /**
     * @Rest\Post("/getAll" , name="get_all")
     */
    public function getAll(Request $request): Response
    {
        $res = $this->orderService->getAll($request);
        $view = $this->view($res["data"], $res['status'], []);
        return $this->handleView($view);
    }

    /**
     * @Rest\Get("/{id}" , name="get_by_id" , requirements={"id"="\d+"})
     */
    public function getById($id): Response
    {
        $res = $this->orderService->getById($id);
        $view = $this->view($res["data"], $res['status'], []);
        return $this->handleView($view);
    }

    /**
     * @Rest\Post("/{id}/paymentstatus/{status}" , name="update_payment_Status")
     */
    public function updatePaymentStatus($id, $status): Response
    {
        $res = $this->orderService->updatePaymentStatus($id, $status);
        $view = $this->view($res["data"], $res['status'], []);
        return $this->handleView($view);
    }


    /**
     * @Rest\Post("/{id}/preparestatus/{status}" , name="update_prepare_Status")
     */
    public function updatePrepareStatus($id, $status): Response
    {
        $res = $this->orderService->updatePrepareStatus($id, $status);
        $view = $this->view($res["data"], $res['status'], []);
        return $this->handleView($view);
    }

    /**
     * @Rest\Get("/payment/status/{status}" , name="get_by_payment_status")
     */
    public function getByPaymentStatus($status): Response
    {
        $res = $this->orderService->getByPaymentStatus($status);
        $view = $this->view($res["data"], $res['status'], []);
        return $this->handleView($view);
    }

    /**
     * @Rest\Get("/prepare/status/{status}" , name="get_by_prepare_status")
     */
    public function getByPrepareStatus($status): Response
    {
        $res = $this->orderService->getByPrepareStatus($status);
        $view = $this->view($res["data"], $res['status'], []);
        return $this->handleView($view);
    }
}
