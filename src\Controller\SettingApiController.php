<?php

namespace App\Controller;

use App\Services\Setting\SettingServiceInterface;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * @Rest\Route("/setting" , name="setting_" )
 */
class SettingApiController extends AbstractFOSRestController
{

    private SettingServiceInterface $settingService;
    public function __construct(SettingServiceInterface $settingService)
    {
        $this->settingService = $settingService;
    }

    /**
     * @Rest\Post("/" , name="update" )
     */
    public function update(Request $request)
    {
        $res = $this->settingService->update($request);
        if ($res['status'] === Response::HTTP_CREATED) {
            $view = $this->view($res["data"], Response::HTTP_CREATED, []);
        } else {
            $view = $this->view($res["data"], $res['status'], []);
        }
        return $this->handleView($view);
    }

    /**
     * @Rest\Get("s" , name="get_all")
     */
    public function getAll()
    {
        $res = $this->settingService->getAll();
        if ($res['status'] === Response::HTTP_OK) {
            $view = $this->view($res["data"], Response::HTTP_OK, []);
        } else {
            $view = $this->view($res["data"], $res['status'], []);
        }
        return $this->handleView($view);
    }

    /**
     * @Rest\Get("/{type}" , name="settings_get_by_type")
     */
    public function getByType(String $type)
    {
        $res = $this->settingService->getSetting($type);
        if ($res['status'] === Response::HTTP_OK) {
            $view = $this->view($res["data"], Response::HTTP_OK, []);
        } else {
            $view = $this->view($res["data"], $res['status'], []);
        }
        return $this->handleView($view);
    }

    /**
     * @Rest\Post("/do_action" , name="do_action")
     */
    public function doAction(Request $request)
    {
        $res = $this->settingService->doAction($request);
        if ($res['status'] === Response::HTTP_OK) {
            $view = $this->view($res["data"], Response::HTTP_OK, []);
        } else {
            $view = $this->view($res["data"], $res['status'], []);
        }
        return $this->handleView($view);
    }
}
