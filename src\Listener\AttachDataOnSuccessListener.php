<?php

namespace App\Listener;

use App\Entity\User;
use Doctrine\ORM\EntityManagerInterface;
use Lexik\Bundle\JWTAuthenticationBundle\Event\AuthenticationSuccessEvent;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;


class AttachDataOnSuccessListener
{
    private $em;
    private $requestStack;
    public function __construct(EntityManagerInterface $em, RequestStack $requestStack)
    {
        $this->em = $em;
        $this->requestStack = $requestStack;
    }

    public function onAuthenticationSuccess(AuthenticationSuccessEvent $event): void
    {
        if ($this->requestStack->getCurrentRequest()->get('_route') === 'api_login_check') {
            $TokenUser = $event->getUser();
            if (!$TokenUser instanceof UserInterface) {
                return;
            }


            $data = $event->getData();
            $user = $this->em->getRepository(User::class)->findOneBy(['username' => $TokenUser->getUserIdentifier()]);
            if (!$user->getIsActive()) {
                $event->getResponse()->headers->clearCookie('BEARER');

                $data = [
                    'code' => Response::HTTP_FORBIDDEN,
                    'message' => 'User is not active',
                ];
                $event->getResponse()->setStatusCode(Response::HTTP_FORBIDDEN);
            } else {
                $data['status'] = 1;
                $data['user'] = $user->getUsername();
                $data['role'] = $user->getRoles()[0];
                if ($user->getRoles()[0] === 'ROLE_COMERCIAL' && $user->getRoles()[1] === "ROLE_ADD_SUB_COMERCIAL") {
                    $data["allowSubUser"] = true;
                }
                if ($user->getRoles()[0] === 'ROLE_COMERCIAL' && $user->getRoles()[1] !== "ROLE_ADD_SUB_COMERCIAL") {
                    $data["allowSubUser"] = false;
                }
                $data['credit'] = $user->getCredit();
                $data['code'] = $user->getCode();
            }



            // Set response data
            $event->setData($data);
        }
    }
}
