<?php

namespace App\Controller;

use App\Services\Tag\TagServiceInterface;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * @Rest\Route("/tag" , name="tag_")
 */
class TagApiController extends AbstractFOSRestController
{

    private TagServiceInterface $tagService;
    public function __construct(TagServiceInterface $tagService)
    {
        $this->tagService = $tagService;
    }

    /**
     * @Rest\Post("/" , name="create")
     */
    public function create(Request $request): Response
    {
        $res = $this->tagService->create($request);
        $view = $this->view($res["data"], $res['status'], []);
        return $this->handleView($view);
    }

    /**
     * @Rest\Get("/" , name="get_all")
     */
    public function getAll(): Response
    {
        $res = $this->tagService->getAll();
        $view = $this->view($res["data"], $res['status'], []);
        return $this->handleView($view);
    }

    /**
     * @Rest\Get("/{id}" , name="get_by_id" , requirements={"id"="\d+"})
     */
    public function getById($id): Response
    {
        $res = $this->tagService->getById($id);
        $view = $this->view($res["data"], $res['status'], []);
        return $this->handleView($view);
    }

    /**
     * @Rest\Post("/{id}" , name="update" , requirements={"id"="\d+"})
     */
    public function update(Request $request, $id): Response
    {
        $res = $this->tagService->update($request, $id);
        $view = $this->view($res["data"], $res['status'], []);
        return $this->handleView($view);
    }

    /**
     * @Rest\Post("/delete/{id}" , name="delete" , requirements={"id"="\d+"})
     */
    public function delete($id): Response
    {
        $res = $this->tagService->delete($id);
        $view = $this->view($res["data"], $res['status'], []);
        return $this->handleView($view);
    }
}
