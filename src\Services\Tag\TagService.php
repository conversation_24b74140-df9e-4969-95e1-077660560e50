<?php

namespace App\Services\Tag;

use App\Entity\Tags;
use App\Entity\Category;
use App\Entity\Options;
use App\Entity\OptionValues;
use Doctrine\ORM\EntityManagerInterface;
use App\Services\Tag\TagServiceInterface;
use App\Utils\Uploader;
use DateTime;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Security\Core\Security;
use Symfony\Component\HttpFoundation\Response;

class TagService implements TagServiceInterface
{

    private EntityManagerInterface $em;
    private Security $security;

    public function __construct(EntityManagerInterface $entityManager, Security $security)
    {
        $this->em = $entityManager;
        $this->security = $security;
    }

    public function getAll(): array
    {
        $res = $this->em->getRepository(Tags::class)->findAll();
        $tags = [];
        foreach ($res as $tag) {
            $tags[] = $tag->toArray();
        }
        return [
            'status' => Response::HTTP_OK,
            'data' => $tags
        ];
    }

    public function getById($id): array
    {
        $tag = $this->em->getRepository(Tags::class)->find($id);
        if (!$tag) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'data' => 'Tag not found'
            ];
        }
        return [
            'status' => Response::HTTP_OK,
            'data' => $tag->toArray()
        ];
    }

    public function create(Request $request): array
    {
        if (!$request->get('name') || !$request->get('color')) {
            return [
                'status' => Response::HTTP_BAD_REQUEST,
                'data' => 'name and color are required'
            ];
        }
        $tag = new Tags($request->get('name'), $request->get('color'));
        $this->em->persist($tag);
        $this->em->flush();
        return [
            'status' => Response::HTTP_OK,
            'data' => $tag->toArray()
        ];
    }

    public function update(Request $request, $id): array
    {
        $tag = $this->em->getRepository(Tags::class)->find($id);
        if (!$tag) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'data' => 'Tag not found'
            ];
        }

        if ($request->get('name')) {
            $tag->setName($request->get('name'));
        }

        if ($request->get('color')) {
            $tag->setColor($request->get('color'));
        }

        $this->em->flush();
        return [
            'status' => Response::HTTP_OK,
            'data' => $tag->toArray()
        ];
    }

    public function delete($id): array
    {
        $tag = $this->em->getRepository(Tags::class)->find($id);
        if (!$tag) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'data' => 'Tag not found'
            ];
        }
        foreach ($tag->getProduct() as $prod) {
            $prod->removeTag($tag);
        }
        $this->em->remove($tag);
        $this->em->flush();
        return [
            'status' => Response::HTTP_OK,
            'data' => ['delete']
        ];
    }
}
