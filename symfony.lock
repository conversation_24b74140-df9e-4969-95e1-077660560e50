{"doctrine/annotations": {"version": "1.13", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.10", "ref": "64d8583af5ea57b7afa4aba4b159907f3a148b05"}}, "doctrine/cache": {"version": "2.1.1"}, "doctrine/collections": {"version": "1.6.8"}, "doctrine/dbal": {"version": "3.3.5"}, "doctrine/deprecations": {"version": "v0.5.3"}, "doctrine/doctrine-bundle": {"version": "2.6", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "2.4", "ref": "ddddd8249dd55bbda16fa7a45bb7499ef6f8e90e"}, "files": ["./config/packages/doctrine.yaml", "./src/Entity/.gitignore", "./src/Repository/.gitignore"]}, "doctrine/doctrine-fixtures-bundle": {"version": "3.5", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.0", "ref": "1f5514cfa15b947298df4d771e694e578d4c204d"}, "files": ["./src/DataFixtures/AppFixtures.php"]}, "doctrine/doctrine-migrations-bundle": {"version": "3.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.1", "ref": "ee609429c9ee23e22d6fa5728211768f51ed2818"}, "files": ["./config/packages/doctrine_migrations.yaml", "./migrations/.gitignore"]}, "doctrine/event-manager": {"version": "1.1.1"}, "doctrine/inflector": {"version": "2.0.4"}, "doctrine/instantiator": {"version": "1.4.1"}, "doctrine/lexer": {"version": "1.2.3"}, "doctrine/migrations": {"version": "3.5.0"}, "doctrine/orm": {"version": "2.11.2"}, "doctrine/persistence": {"version": "2.4.1"}, "doctrine/sql-formatter": {"version": "1.1.2"}, "friendsofphp/proxy-manager-lts": {"version": "v1.0.7"}, "friendsofsymfony/rest-bundle": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "2.2", "ref": "fa845143b7e0a4c70aedd1a88c549e6d977e9ae5"}, "files": ["./config/packages/fos_rest.yaml"]}, "gesdinet/jwt-refresh-token-bundle": {"version": "v1.0.1"}, "jms/metadata": {"version": "2.6.1"}, "jms/serializer": {"version": "3.17.1"}, "jms/serializer-bundle": {"version": "4.0", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "3.0", "ref": "384cec52df45f3bfd46a09930d6960a58872b268"}, "files": ["./config/packages/dev/jms_serializer.yaml", "./config/packages/jms_serializer.yaml", "./config/packages/prod/jms_serializer.yaml"]}, "laminas/laminas-code": {"version": "4.5.1"}, "lcobucci/clock": {"version": "2.1.0"}, "lcobucci/jwt": {"version": "4.0.4"}, "lexik/jwt-authentication-bundle": {"version": "2.15", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "2.5", "ref": "5b2157bcd5778166a5696e42f552ad36529a07a6"}, "files": ["./config/packages/lexik_jwt_authentication.yaml"]}, "monolog/monolog": {"version": "2.5.0"}, "namshi/jose": {"version": "7.2.3"}, "nikic/php-parser": {"version": "v4.13.2"}, "phpstan/phpdoc-parser": {"version": "1.4.3"}, "psr/cache": {"version": "3.0.0"}, "psr/container": {"version": "2.0.2"}, "psr/event-dispatcher": {"version": "1.0.0"}, "psr/log": {"version": "3.0.0"}, "sensio/framework-extra-bundle": {"version": "6.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.2", "ref": "fb7e19da7f013d0d422fa9bce16f5c510e27609b"}, "files": ["./config/packages/sensio_framework_extra.yaml"]}, "symfony-bundles/json-request-bundle": {"version": "4.1.1"}, "symfony/cache": {"version": "v6.0.6"}, "symfony/cache-contracts": {"version": "v3.0.1"}, "symfony/config": {"version": "v6.0.7"}, "symfony/console": {"version": "6.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.3", "ref": "da0c8be8157600ad34f10ff0c9cc91232522e047"}, "files": ["./bin/console"]}, "symfony/dependency-injection": {"version": "v6.0.7"}, "symfony/deprecation-contracts": {"version": "v3.0.1"}, "symfony/doctrine-bridge": {"version": "v6.0.7"}, "symfony/dotenv": {"version": "v6.0.5"}, "symfony/error-handler": {"version": "v6.0.7"}, "symfony/event-dispatcher": {"version": "v6.0.3"}, "symfony/event-dispatcher-contracts": {"version": "v3.0.1"}, "symfony/filesystem": {"version": "v6.0.7"}, "symfony/finder": {"version": "v6.0.3"}, "symfony/flex": {"version": "2.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "c0eeb50665f0f77226616b6038a9b06c03752d8e"}, "files": ["./.env"]}, "symfony/framework-bundle": {"version": "6.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.4", "ref": "3cd216a4d007b78d8554d44a5b1c0a446dab24fb"}, "files": ["./config/packages/cache.yaml", "./config/packages/framework.yaml", "./config/preload.php", "./config/routes/framework.yaml", "./config/services.yaml", "./public/index.php", "./src/Controller/.gitignore", "./src/Kernel.php"]}, "symfony/http-foundation": {"version": "v6.0.7"}, "symfony/http-kernel": {"version": "v6.0.7"}, "symfony/maker-bundle": {"version": "1.38", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "fadbfe33303a76e25cb63401050439aa9b1a9c7f"}}, "symfony/monolog-bridge": {"version": "v6.0.3"}, "symfony/monolog-bundle": {"version": "3.7", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.7", "ref": "213676c4ec929f046dfde5ea8e97625b81bc0578"}, "files": ["./config/packages/monolog.yaml"]}, "symfony/password-hasher": {"version": "v6.0.3"}, "symfony/polyfill-intl-grapheme": {"version": "v1.25.0"}, "symfony/polyfill-intl-normalizer": {"version": "v1.25.0"}, "symfony/polyfill-mbstring": {"version": "v1.25.0"}, "symfony/polyfill-php56": {"version": "v1.20.0"}, "symfony/polyfill-php81": {"version": "v1.25.0"}, "symfony/property-access": {"version": "v6.0.7"}, "symfony/property-info": {"version": "v6.0.7"}, "symfony/proxy-manager-bridge": {"version": "v6.0.6"}, "symfony/routing": {"version": "6.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "6.0", "ref": "eb3b377a4dc07006c4bdb2c773652cc9434f5246"}, "files": ["./config/packages/routing.yaml", "./config/routes.yaml"]}, "symfony/runtime": {"version": "v6.0.7"}, "symfony/security-bundle": {"version": "6.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.3", "ref": "98f1f2b0d635908c2b40f3675da2d23b1a069d30"}, "files": ["./config/packages/security.yaml"]}, "symfony/security-core": {"version": "v6.0.7"}, "symfony/security-csrf": {"version": "v6.0.3"}, "symfony/security-http": {"version": "v6.0.7"}, "symfony/service-contracts": {"version": "v3.0.1"}, "symfony/stopwatch": {"version": "v6.0.5"}, "symfony/string": {"version": "v6.0.3"}, "symfony/translation-contracts": {"version": "v3.0.1"}, "symfony/validator": {"version": "6.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.3", "ref": "c32cfd98f714894c4f128bb99aa2530c1227603c"}, "files": ["./config/packages/validator.yaml"]}, "symfony/var-dumper": {"version": "v6.0.6"}, "symfony/var-exporter": {"version": "v6.0.7"}, "symfony/yaml": {"version": "v6.0.3"}, "willdurand/jsonp-callback-validator": {"version": "v2.0.0"}, "willdurand/negotiation": {"version": "3.1.0"}, "zenstruck/foundry": {"version": "1.36", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.10", "ref": "37c2f894cc098ab4c08874b80cccc8e2f8de7976"}, "files": ["./config/packages/zenstruck_foundry.yaml"]}}