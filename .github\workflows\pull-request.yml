name: Pull-Request Workflow
on:
  pull_request:
    branches:
      - main

jobs:
  Build-Docker-php:
    runs-on: pos-terminal-team-scale-set
    steps:
      - name: Checkout repository 📥
        uses: actions/checkout@v4

      - name: Build Docker image 🐳
        id: push
        uses: docker/build-push-action@v5
        with:
          context: .
          push: false
          target: prod

  Build-Docker-nginx:
    runs-on: pos-terminal-team-scale-set
    steps:
      - name: Checkout repository 📥
        uses: actions/checkout@v4

      - name: Build Docker image 🐳
        id: push
        uses: docker/build-push-action@v5
        with:
          context: .
          push: false
          target: nginx
