<?php

namespace App\Controller;

use App\Services\Terminal\TerminalServiceInterface;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * @Rest\Route("/terminal" , name="terminal_")
 */
class TerminalApiController extends AbstractFOSRestController
{

    private TerminalServiceInterface $terminalService;
    public function __construct(TerminalServiceInterface $terminalService)
    {
        $this->terminalService = $terminalService;
    }

    /**
     * @Rest\Get("/ping" , name="ping")
     */
    public function ping(Request $request): Response
    {
        $res = $this->terminalService->ping($request);
        $view = $this->view($res["data"], $res['status'], []);
        return $this->handleView($view);
    }

    /**
     * @Rest\Post("/subscribe" , name="subscribe")
     */
    public function subscribe(Request $request): Response
    {
        $res = $this->terminalService->subscribe($request);
        $view = $this->view($res["data"], $res['status'], []);
        return $this->handleView($view);
    }
}
