<?php

namespace App\Services\Printer;

use App\Entity\Category;
use App\Entity\Printer;
use App\Services\Printer\PrinterServiceInterface;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Security\Core\Security;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Process\Process;
use Mike42\Escpos\PrintConnectors\NetworkPrintConnector;

class PrinterService implements PrinterServiceInterface
{

    private $em;
    private $security;

    public function __construct(
        EntityManagerInterface $entityManager,
        Security               $security
    ) {
        $this->em = $entityManager;
        $this->security = $security;
    }

    public function update(Request $request)
    {
        $printerId = $request->get('id');
        $ipAddress = $request->get('ip_address');
        $isMain = $request->get('is_main', false);
        $categories = $request->get('categories', []);
        if (!$ipAddress) {
            return [
                'status' => Response::HTTP_BAD_REQUEST,
                'data' => 'ip_address is required'
            ];
        }
        if (!$this->validatePrinterIp($ipAddress)) {
            return [
                'status' => Response::HTTP_BAD_REQUEST,
                'data' => 'ip_address is not valid'
            ];
        }
        $printer = $this->em->getRepository(Printer::class)->find($printerId);
        if (!$printer) {
            return [
                'status' => Response::HTTP_BAD_REQUEST,
                'data' => 'printer not found'
            ];
        }
        $printer->setIpAdress($ipAddress);
        $printer->setIsMain($isMain);
        foreach ($printer->getCategory() as $cat) {
            $printer->removeCategory($cat);
        }
        foreach ($categories as $cat) {
            $category = $this->em->getRepository(Category::class)->find($cat);
            if (!$category) {
                return [
                    'status' => Response::HTTP_BAD_REQUEST,
                    'data' => 'category not found'
                ];
            }
            $printer->addCategory($category);
        }

        $this->em->flush();
        return [
            'status' => Response::HTTP_OK,
            'data' => ['done']
        ];
    }


    public function validatePrinterIp($ip)
    {
        return true;
        try {
            new NetworkPrintConnector($ip, port: 9100);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }


    public function add(Request $request)
    {
        $ipAddress = $request->get('ip_address');
        $isMain = $request->get('is_main', false);
        $categories = $request->get('categories', []);
        if (!$ipAddress) {
            return [
                'status' => Response::HTTP_BAD_REQUEST,
                'data' => 'ip_address is required'
            ];
        }
        if (!$this->validatePrinterIp($ipAddress)) {
            return [
                'status' => Response::HTTP_BAD_REQUEST,
                'data' => 'ip_address is not valid'
            ];
        }
        $printer = new Printer($ipAddress, $isMain);
        foreach ($categories as $cat) {
            $category = $this->em->getRepository(Category::class)->find($cat);
            if (!$category) {
                return [
                    'status' => Response::HTTP_BAD_REQUEST,
                    'data' => 'category not found'
                ];
            }
            $printer->addCategory($category);
        }

        $this->em->persist($printer);
        $this->em->flush();
        return [
            'status' => Response::HTTP_OK,
            'data' => ['done']
        ];
    }

    public function getAll()
    {
        $printers = $this->em->getRepository(Printer::class)->findAll();
        $result = [];
        foreach ($printers as $printer) {
            $result[] = $printer->toArray();
        }
        return [
            'status' => Response::HTTP_OK,
            'data' => $result
        ];
    }

    public function delete(int $id)
    {
        $printer = $this->em->getRepository(Printer::class)->find($id);
        if (!$printer) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'data' => 'Printer Not Found'
            ];
        }
        foreach ($printer->getCategory() as $cat) {
            $printer->removeCategory($cat);
        }
        $this->em->remove($printer);
        $this->em->flush();
        return [
            'status' => Response::HTTP_OK,
            'data' => ['deleted']
        ];
    }

    public function doAction(Request $request)
    {
        $action = $request->get('action');
        return match ($action) {
            default => [
                'status' => Response::HTTP_BAD_REQUEST,
                'data' => 'action not found'
            ],
        };
    }
}
