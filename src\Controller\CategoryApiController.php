<?php

namespace App\Controller;

use App\Services\Category\CategoryServiceInterface;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * @Rest\Route("/category" , name="category")
 */
class CategoryApiController extends AbstractFOSRestController
{

    private $categoryService;
    public function __construct(CategoryServiceInterface $categoryService)
    {
        $this->categoryService = $categoryService;
    }

    /**
     * @Rest\Post("/" , name="create" )
     */
    public function create(Request $request)
    {
        $res = $this->categoryService->addCategory($request);
        if ($res['status'] === Response::HTTP_CREATED) {
            $view = $this->view($res["data"], Response::HTTP_CREATED, []);
        } else {
            $view = $this->view($res["message"], $res['status'], []);
        }
        return $this->handleView($view);
    }

    /**
     * @Rest\Get("/" , name="all" )
     */
    public function getAll()
    {
        $res = $this->categoryService->getAllCategories();
        if ($res['status'] === Response::HTTP_OK) {
            $view = $this->view($res["data"], Response::HTTP_OK, []);
        } else {
            $view = $this->view($res["data"], $res['status'], []);
        }
        return $this->handleView($view);
    }


    /**
     * @Rest\Get("/{id}" , name="by_id" , requirements={"id"="\d+"})
     */
    public function getById($id)
    {
        $res = $this->categoryService->getCategoryById($id);
        if ($res['status'] === Response::HTTP_OK) {
            $view = $this->view($res["data"], Response::HTTP_OK, []);
        } else {
            $view = $this->view($res["data"], $res['status'], []);
        }
        return $this->handleView($view);
    }

    /**
     * @Rest\Post("/delete/{id}" , name="delete" , requirements={"id"="\d+"})
     */
    public function delete($id)
    {
        $res = $this->categoryService->deleteCategory($id);
        if ($res['status'] === Response::HTTP_OK) {
            $view = $this->view($res["data"], Response::HTTP_OK, []);
        } else {
            $view = $this->view($res["data"], $res['status'], []);
        }
        return $this->handleView($view);
    }

    /**
     * @Rest\Post("/{id}" , name="update" , requirements={"id"="\d+"})
     */
    public function update(Request $request, $id)
    {
        $res = $this->categoryService->updateCategory($request, $id);
        if ($res['status'] === Response::HTTP_OK) {
            $view = $this->view($res["data"], Response::HTTP_OK, []);
        } else {
            $view = $this->view($res["data"], $res['status'], []);
        }
        return $this->handleView($view);
    }

    /**
     * @Rest\Post("/doAction" , name="do_action")
     */
    public function doAction(Request $request)
    {
        $res = $this->categoryService->doAction($request);
        if ($res['status'] === Response::HTTP_OK) {
            $view = $this->view($res["data"], Response::HTTP_OK, []);
        } else {
            $view = $this->view($res["data"], $res['status'], []);
        }
        return $this->handleView($view);
    }
}
