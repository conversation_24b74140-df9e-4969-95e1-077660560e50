<?php

namespace App\Controller;

use App\Services\OptionValues\OptionValuesServiceInterface;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * @Rest\Route("/optionValues" , name="optionValues_")
 */
class OptionValuesApiController extends AbstractFOSRestController
{

    private $optionValuesService;
    public function __construct(OptionValuesServiceInterface $optionValuesService)
    {
        $this->optionValuesService = $optionValuesService;
    }

    /**
     * @Rest\Get("/" , name="get_all")
     */
    public function getAllOptionValues()
    {
        $res = $this->optionValuesService->getOptionValues();
        $view = $this->view($res["data"], $res['status'], []);
        return $this->handleView($view);
    }

    /**
     * @Rest\Post("/" , name="create")
     */
    public function postOptionValue(Request $request)
    {
        $res = $this->optionValuesService->addOptionValue($request);
        $view = $this->view($res["data"], $res['status'], []);
        return $this->handleView($view);
    }

    /**
     * @Rest\Get("/{id}" , name="by_id" , requirements={"id"="\d+"})
     */
    public function getOptionValueById($id)
    {
        $res = $this->optionValuesService->getOptionValueById($id);
        $view = $this->view($res["data"], $res['status'], []);
        return $this->handleView($view);
    }

    /**
     * @Rest\Post("/delete/{id}" , name="delete" , requirements={"id"="\d+"})
     */
    public function deleteOptionValue($id)
    {
        $res = $this->optionValuesService->deleteOptionValue($id);
        $view = $this->view($res["data"], $res['status'], []);
        return $this->handleView($view);
    }

    /**
     * @Rest\Post("/{id}" , name="update" , requirements={"id"="\d+"})
     */
    public function updateOptionValue(Request $request, $id)
    {
        $res = $this->optionValuesService->updateOptionValue($request, $id);
        $view = $this->view($res["data"], $res['status'], []);
        return $this->handleView($view);
    }

    /**
     * @Rest\Post("/doAction" , name="do_action")
     */
    public function doAction(Request $request)
    {
        $res = $this->optionValuesService->doAction($request);
        $view = $this->view($res["data"], $res['status'], []);
        return $this->handleView($view);
    }
}
