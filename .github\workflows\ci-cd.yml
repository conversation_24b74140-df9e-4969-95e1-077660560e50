name: CI/CD Workflow
on:
  push:
    branches:
      - main

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  Build-Docker-php:
    runs-on: pos-terminal-team-scale-set
    permissions:
      contents: read
      packages: write
      attestations: write
      id-token: write
    steps:
      - name: Checkout repository 📥
        uses: actions/checkout@v4

      - name: Log in to the Github Container registry 🔑
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}  

      - name: Extract metadata (tags, labels) for Docker ℹ️
        id: php-meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-php

      - name: Build Docker image 🐳
        id: push
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: ${{ steps.php-meta.outputs.tags }}
          labels: ${{ steps.php-meta.outputs.labels }}
          target: prod

  Build-Docker-nginx:
    runs-on: pos-terminal-team-scale-set
    permissions:
      contents: read
      packages: write
      attestations: write
      id-token: write
    steps:
      - name: Checkout repository 📥
        uses: actions/checkout@v4

      - name: Log in to the Github Container registry 🔑
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}  

      - name: Extract metadata (tags, labels) for Docker ℹ️
        id: nginx-meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-nginx 

      - name: Build Docker image 🐳
        id: push
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: ${{ steps.nginx-meta.outputs.tags }}
          labels: ${{ steps.nginx-meta.outputs.labels }}
          target: nginx
