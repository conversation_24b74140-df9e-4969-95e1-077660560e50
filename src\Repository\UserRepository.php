<?php

namespace App\Repository;

use App\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\ORMException;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Component\Security\Core\Exception\UnsupportedUserException;
use Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface;
use Symfony\Component\Security\Core\User\PasswordUpgraderInterface;

/**
 * @method User|null find($id, $lockMode = null, $lockVersion = null)
 * @method User|null findOneBy(array $criteria, array $orderBy = null)
 * @method User[]    findAll()
 * @method User[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class UserRepository extends ServiceEntityRepository implements PasswordUpgraderInterface
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, User::class);
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function add(User $entity, bool $flush = true): void
    {
        $this->_em->persist($entity);
        if ($flush) {
            $this->_em->flush();
        }
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function remove(User $entity, bool $flush = true): void
    {
        $this->_em->remove($entity);
        if ($flush) {
            $this->_em->flush();
        }
    }

    /**
     * Used to upgrade (rehash) the user's password automatically over time.
     */
    public function upgradePassword(PasswordAuthenticatedUserInterface $user, string $newHashedPassword): void
    {
        if (!$user instanceof User) {
            throw new UnsupportedUserException(sprintf('Instances of "%s" are not supported.', \get_class($user)));
        }

        $user->setPassword($newHashedPassword);
        $this->_em->persist($user);
        $this->_em->flush();
    }
    ///////////////////////////////////
    // /**
    //  * @return User[] Returns an array of User objects
    //  */
    /*
    public function findByExampleField($value)
    {
        return $this->createQueryBuilder('u')
            ->andWhere('u.exampleField = :val')
            ->setParameter('val', $value)
            ->orderBy('u.id', 'ASC')
            ->setMaxResults(10)
            ->getQuery()
            ->getResult()
        ;
    }
    */

    /*
    public function findOneBySomeField($value): ?User
    {
        return $this->createQueryBuilder('u')
            ->andWhere('u.exampleField = :val')
            ->setParameter('val', $value)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }
    */

    public function getAll($search, $currentPage, $pageSize, $sort, $filters, $dateType) //$start, $end,
    {
        $qb = $this->createQueryBuilder('u');
        $qb->select("
                u.id,
                u.username,
                u.createdAt,
                u.isActive,
                u.credit,
                u.roles
                ");
        $qb
            ->where(
                $qb->expr()->orX(
                    $qb->expr()->like('u.username', ':search'),
                )
            )
            ->setParameter('search', '%' . $search . '%')
            ->setFirstResult(($currentPage - 1) * $pageSize)
            ->setMaxResults($pageSize)
        ;
        if ($sort) {
            if ($sort['field'] === 'username')
                $sort['field'] = 'username';
            $qb->orderBy('u.' . $sort['field'], $sort['order'] === 'ascend' ? 'ASC' : 'DESC');
        }
        $filterConditions = [
            'isActive' => 'u.isActive IN (:isActive)',
        ];

        $filterLike = [
            'id' => 'u.id LIKE :id',
            'username' => 'u.username LIKE :username',
            'credit' => 'u.credit LIKE :credit',
        ];

        $filterDate = [
            'createdAt' => 'u.createdAt BETWEEN :start_createdAt and :end_createdAt',
        ];

        foreach ($filters as $key => $filter) {

            if ($filter !== null && array_key_exists($key, $filterConditions)) {
                $qb->andWhere($filterConditions[$key])->setParameter($key, $filter);
            }
            if ($filter !== null && array_key_exists($key, $filterLike)) {
                $qb->andWhere($filterLike[$key])->setParameter($key, '%' . $filter[0] . '%');
            }
            if ($filter !== null && array_key_exists($key, $filterDate)) {
                $qb->andWhere($filterDate[$key])->setParameter('start_' . $key, new \DateTime($filter[0][0]))
                    ->setParameter('end_' . $key, new \DateTime($filter[0][1]));
            }
        }

        return $qb->getQuery()->getResult();
    }
    public function getAllCount($search, $filters, $dateType)
    {
        $qb = $this->createQueryBuilder('u');
        $qb
            ->select('COUNT(u.id)')
            ->andWhere(
                $qb->expr()->orX(
                    $qb->expr()->like(' u.username', ':search'),
                )
            )
            ->setParameter('search', '%' . $search . '%')
        ;
        $filterConditions = [
            'isActive' => 'u.isActive IN (:isActive)',
        ];
        $filterLike = [
            'id' => 'u.id LIKE :id',
            'username' => 'u.username LIKE :username',
            'credit' => 'u.credit LIKE :credit',
        ];
        $filterDate = [
            'createdAt' => 'u.createdAt between :start_createdAt and :end_createdAt',
        ];

        foreach ($filters as $key => $filter) {

            if ($filter !== null && array_key_exists($key, $filterConditions)) {
                $qb->andWhere($filterConditions[$key])->setParameter($key, $filter);
            }
            if ($filter !== null && array_key_exists($key, $filterLike)) {
                $qb->andWhere($filterLike[$key])->setParameter($key, '%' . $filter[0] . '%');
            }
            if ($filter !== null && array_key_exists($key, $filterDate)) {
                $qb->andWhere($filterDate[$key])->setParameter('start_' . $key, new \DateTime($filter[0][0]))
                    ->setParameter('end_' . $key, new \DateTime($filter[0][1]));
            }
        }

        return (int) $qb->getQuery()->getSingleScalarResult();
    }

    public function getAdmin()
    {
        return $this->createQueryBuilder('u')
            ->where('u.roles LIKE :roles')
            ->setParameter('roles', '%ROLE_ADMIN%')
            ->getQuery()
            ->setMaxResults(1)
            ->getOneOrNullResult();
    }
}
