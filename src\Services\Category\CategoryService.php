<?php

namespace App\Services\Category;

use App\Entity\Category;
use Doctrine\ORM\EntityManagerInterface;
use App\Services\Category\CategoryServiceInterface;
use App\Utils\Uploader;
use DateTime;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Security\Core\Security;
use Symfony\Component\HttpFoundation\Response;

class CategoryService implements CategoryServiceInterface
{

    private EntityManagerInterface $em;
    private Security $security;

    public function __construct(EntityManagerInterface $entityManager, Security $security)
    {
        $this->em = $entityManager;
        $this->security = $security;
    }

    public function getCategoryById(int $id): array
    {
        $category = $this->em->getRepository(Category::class)->find($id);
        return [
            'status' => Response::HTTP_OK,
            'data' => $category->toArray()
        ];
    }

    public function getAllCategories(): array
    {
        $categories = $this->em->getRepository(Category::class)->findAll();
        $result = [];
        foreach ($categories as $category) {
            if ($category->getParent() != null) {
                continue;
            }
            $result[] = $category->toArray();
        }
        return [
            'status' => Response::HTTP_OK,
            'data' => $result
        ];
    }

    public function addCategory(Request $request): array
    {
        if (!$this->security->isGranted('ROLE_ADMIN')) {
            return [
                'status' => Response::HTTP_FORBIDDEN,
                "message" => 'you are not allowed to create this category'
            ];
        }
        $category = $this->em->getRepository(Category::class)->findOneBy(['name' => $request->request->get('name')]);
        if ($category) {
            return [
                'status' => Response::HTTP_CONFLICT,
                "message" => 'category already exists'
            ];
        }
        $parent = null;
        if ($request->get('parent_id')) {
            $parent = $this->em->getRepository(Category::class)->find($request->get('parent_id'));
            if (!$parent) {
                return [
                    'status' => Response::HTTP_NOT_FOUND,
                    "message" => 'category not found'
                ];
            }
        }
        if (!$parent && !$request->files->get('image')) {
            return [
                'status' => Response::HTTP_BAD_REQUEST,
                "message" => 'Image is required on Main Category'
            ];
        }

        $category = new Category();
        $category->setName(trim(strtolower($request->request->get('name'))));
        $category->setCreatedBy($this->security->getUser()->getUserIdentifier());
        $category->setModifedBy($this->security->getUser()->getUserIdentifier());
        if ($request->files->get('image')) {
            $imagelink = Uploader::uploadImageToWebp($request->files->get('image'), 'category');
            $category->setImage($imagelink);
        }
        if ($parent) {
            $parent->addSubCategory($category);
            $category->setParent($parent);
        }
        $this->em->persist($category);
        $this->em->flush();
        return [
            'status' => Response::HTTP_CREATED,
            'data' => $category->toArray()
        ];
    }

    public function updateCategory(Request $request, int $id): array
    {
        $category = $this->em->getRepository(Category::class)->find($id);
        if (!$category) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                "data" => 'category not found'
            ];
        }
        if (!$this->security->isGranted('ROLE_ADMIN')) {
            return [
                'status' => Response::HTTP_FORBIDDEN,
                "data" => 'you are not allowed to update this category'
            ];
        }
        $parent = null;
        if ($request->get('parent_id') && $request->get('parent_id') != 'none') {
            $parent = $this->em->getRepository(Category::class)->find($request->get('parent_id'));
            if (!$parent) {
                return [
                    'status' => Response::HTTP_NOT_FOUND,
                    "data" => 'category not found'
                ];
            }
        }
        if ($parent) {
            $parent->addSubCategory($category);
            $category->setParent($parent);
        }
        if ($request->get('parent_id') === 'none') {
            $category->setParent(null);
        }
        if ($request->request->get('name')) {
            $category->setName(trim(strtolower($request->request->get('name'))));
        }
        $category->getModifedAt(new DateTime());
        $category->getModifedBy($this->security->getUser()->getUserIdentifier());
        $this->em->flush();
        return [
            'status' => Response::HTTP_OK,
            'data' => $category->toArray()
        ];
    }

    public function changeCategoryImage(Request $request, int $id): array
    {
        $category = $this->em->getRepository(Category::class)->find($id);
        if (!$category) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                "message" => 'category not found'
            ];
        }
        if (!$this->security->isGranted('ROLE_ADMIN')) {
            return [
                'status' => Response::HTTP_FORBIDDEN,
                "message" => 'you are not allowed to update this category'
            ];
        }
        if (!$request->files->get('image')) {
            return [
                'status' => Response::HTTP_BAD_REQUEST,
                "message" => 'Image is required'
            ];
        }
        if ($request->files->get('image')) {
            $oldImage = $category->getImage();
            $imagelink = Uploader::uploadImageToWebp($request->files->get('image'), 'category');
            $category->setImage($imagelink);
            if ($oldImage) {
                Uploader::deleteFile($oldImage);
            }
        }
        $this->em->flush();
        return [
            'status' => Response::HTTP_OK,
            'data' => $category->toArray()
        ];
    }

    public function deleteCategory(int $id): array
    {
        $category = $this->em->getRepository(Category::class)->find($id);
        $defaultCategory = $this->em->getRepository(Category::class)->find(1);
        if (!$category) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'data' => 'category not found'
            ];
        }
        if (!$this->security->isGranted('ROLE_ADMIN')) {
            return [
                'status' => Response::HTTP_FORBIDDEN,
                'data' => 'you are not allowed to delete this category'
            ];
        }
        $subCategories = $category->getSubCategories();
        foreach ($subCategories as $subCategory) {
            $this->em->remove($subCategory);
        }
        $this->em->remove($category);
        if ($category->getProducts()->count() > 0) {
            foreach ($category->getProducts() as $product) {
                $product->setCateory($defaultCategory);
                $product->setModifedAt(new DateTime());
                $product->setSubCategory(null);
            }
        }
        if ($category->getImage())
            Uploader::deleteFile($category->getImage());
        $this->em->flush();
        return [
            'status' => Response::HTTP_OK,
            'data' => ['category deleted']
        ];
    }

    public function doAction(Request $request): array
    {
        $action = $request->request->get('action');
        if (!$action) {
            return [
                'status' => Response::HTTP_BAD_REQUEST,
                "message" => 'action not found'
            ];
        }
        switch ($action) {
            case 'changeCategoryImage':
                return $this->changeCategoryImage($request, $request->request->get('id'));
            default:
                return [
                    'status' => Response::HTTP_BAD_REQUEST,
                    "message" => 'action not found'
                ];
        }
    }
}
