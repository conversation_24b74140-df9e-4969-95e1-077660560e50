<?php

namespace App\Services\User;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;

interface UserServiceInterface
{
    public function register(Request $request);
    public function addSubUser(Request $request);
    public function allSubUsers();
    public function getUsernames();
    public function getSubUser(int $userID);
    public function changeUserPassword(Request $request);
    public function changePassword(Request $request);
    public function doAction(Request $request);
    public function delete(int $id);
    public function getUserById(int $id);
    public function getAllUsers(Request $request);
    public function update(Request $request, int $id);
}
