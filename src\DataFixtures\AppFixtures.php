<?php

namespace App\DataFixtures;

use App\Entity\Media;
use App\Entity\User;
use App\Factory\CategoryFactory;
use App\Factory\MediaFactory;
use App\Factory\ProductsFactory;
use App\Factory\TagsFactory;
use App\Factory\TerminalsFactory;
use App\Factory\UserFactory;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;

class AppFixtures extends Fixture
{
    private $passwordHasher;

    public function __construct(UserPasswordHasherInterface $passwordHasher)
    {
        $this->passwordHasher = $passwordHasher;
    }



    public function load(ObjectManager $manager): void
    {
        UserFactory::createMany(10);
        UserFactory::createOne([
            'code' => 'AAAAAAAA',
            'createdAt' => new \DateTime(),
            'credit' => 1000000,
            'isActive' => true,
            'password' => $this->passwordHasher->hashPassword(new User(), '123456789'),
            'roles' => ['ROLE_ADMIN'],
            'username' => 'syAdmin',
        ]);
        TagsFactory::createMany(5);
        CategoryFactory::createSequence([
            [
                'createdAt' => new \DateTime(),
                'createdBy' => 'system',
                'modifedAt' => new \DateTime(),
                'modifedBy' => 'system',
                'name' => 'Burgers',
                'image' => '/images/category/burgers-670d269a53f8b7.68231424.webp',
            ],
            [
                'createdAt' => new \DateTime(),
                'createdBy' => 'system',
                'modifedAt' => new \DateTime(),
                'modifedBy' => 'system',
                'name' => 'Pizza',
                'image' => '/images/category/pizza-670d294e59ec68.94523044.webp',
            ],
            [
                'createdAt' => new \DateTime(),
                'createdBy' => 'system',
                'modifedAt' => new \DateTime(),
                'modifedBy' => 'system',
                'name' => 'Sides',
                'image' => '/images/category/product-12-670d339fa890b7.13651569.webp',
            ],
            [
                'createdAt' => new \DateTime(),
                'createdBy' => 'system',
                'modifedAt' => new \DateTime(),
                'modifedBy' => 'system',
                'name' => 'Desserts',
                'image' => '/images/category/product-23-670d33596c4918.58284850.webp',
            ],
            [
                'createdAt' => new \DateTime(),
                'createdBy' => 'system',
                'modifedAt' => new \DateTime(),
                'modifedBy' => 'system',
                'name' => 'Drinks',
                'image' => '/images/category/product-3-670d3382c75671.77963276.webp',
            ],
            [
                'createdAt' => new \DateTime(),
                'createdBy' => 'system',
                'modifedAt' => new \DateTime(),
                'modifedBy' => 'system',
                'name' => 'Salads',
                'image' => '/images/category/salade-670d31f38d3487.23708159.webp',
            ],
            [
                'createdAt' => new \DateTime(),
                'createdBy' => 'system',
                'modifedAt' => new \DateTime(),
                'modifedBy' => 'system',
                'name' => 'sandwiches',
                'image' => '/images/category/product-13-670d3298de3f33.51131717.webp',
            ]
        ]);
        MediaFactory::createSequence([
            [
                'type' => 'home-banner',
                'link' => '/videos/media/vid1.mp4',
            ],
            [
                'type' => 'home-banner',
                'link' => '/videos/media/crystal-serum-video-ads---made-with-postermywall--1---online-video-cutter-com-67113985abac04.83187928.mp4',
            ],
            [
                'type' => 'menu-banner',
                'link' => '/images/media/healthy-food-restaurant-banner-design-template-5d8526f015d6a01027536b17714b98d3-screen-671545d5295d68.16591785.webp',
            ],
            [
                'type' => 'menu-banner',
                'link' => '/images/media/preview-restaurant-food-banner-template-design-**********-671545ec5c12b4.49103021.webp',
            ],
            [
                'type' => 'menu-banner',
                'link' => '/images/media/burger-poster-design-template-471879a9187123f71eefd3c08edd0000-screen-671546914c3028.17924003.webp',
            ],
            [
                'type' => 'menu-banner',
                'link' => '/images/media/preview-restaurant-food-poster-banner-download-from-coreldrawdesign-**********-6715572db85e23.76465726.webp',
            ]
        ]);
        TerminalsFactory::createOne([
            'apiKey' => 'testKey',
            'isOnline' => true,
            'status' => 'active',
        ]);
        ProductsFactory::createSequence([
            [
                'category' => CategoryFactory::random(),
                'createdAt' => new \DateTime(),
                'createdBy' => 'system',
                'description' => 'Hamburger with cheese',
                'image' => '/images/products/product-3-670d636ebf2831.47117094.webp',
                'isAddon' => true,
                'isCustomizable' => false,
                'isFeatured' => true,
                'isPack' => false,
                'modifedBy' => 'system',
                'modifiedAt' => new \DateTime(),
                'name' => 'Coca Cola',
                'price' => 1.8,
            ],
            [
                'category' => CategoryFactory::random(),
                'createdAt' => new \DateTime(),
                'createdBy' => 'system',
                'description' => 'Hamburger with cheese',
                'image' => '/images/products/product-6-670d68ac763552.33503020.webp',
                'isAddon' => true,
                'isCustomizable' => false,
                'isFeatured' => true,
                'isPack' => false,
                'modifedBy' => 'system',
                'modifiedAt' => new \DateTime(),
                'name' => 'Mini Fries',
                'price' => 2,
            ]
        ]);

        $manager->flush();
    }
}
