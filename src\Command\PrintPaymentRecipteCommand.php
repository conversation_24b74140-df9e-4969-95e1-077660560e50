<?php

namespace App\Command;

use App\Entity\Options;
use App\Entity\OptionValues;
use App\Entity\Order;
use App\Entity\Printer;
use App\Entity\Settings;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Mike42\Escpos\PrintConnectors\NetworkPrintConnector;
use Mike42\Escpos\PrintConnectors\WindowsPrintConnector;
use Mike42\Escpos\Printer as PrinterLib;

class PrintPaymentRecipteCommand extends Command
{
    protected static $defaultName = 'payment:print';
    private $em;

    public function __construct(EntityManagerInterface $em, HttpClientInterface $httpClient)
    {
        $this->em = $em;
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addArgument('orderId', null, 'Argument description')
            ->setDescription('Payment Printer Command')
            ->setHelp('This command allows you to printe the payment receipt');
    }

    /**
     * @throws \Exception
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $orderId = $input->getArgument('orderId');
        $order = $this->em->getRepository(Order::class)->find($orderId);
        if (!$order) {
            $output->writeln('Order Not Found');
            return Command::FAILURE;
        }
        $mainPrinter = $this->em->getRepository(Printer::class)->findOneBy(['isMain' => true]);
        if (!$mainPrinter) {
            $output->writeln('Main Printer Not Found');
            return Command::FAILURE;
        }

        $currency = $this->em->getRepository(Settings::class)->findOneBy(['name' => 'currency'])->getAttribute();
        if (!$currency) {
            $currency = 'DT';
        }
        $storeName = $this->em->getRepository(Settings::class)->findOneBy(['name' => 'resturant_name'])->getAttribute();
        if (!$storeName) {
            $storeName = 'Le Domaine';
        }
        $storeEmail = $this->em->getRepository(Settings::class)->findOneBy(['name' => 'resturant_email'])->getAttribute();
        $storePhone = $this->em->getRepository(Settings::class)->findOneBy(['name' => 'resturant_phone'])->getAttribute();
        $storeAddress = $this->em->getRepository(Settings::class)->findOneBy(['name' => 'resturant_address'])->getAttribute();



        $orderItems = $order->getOrderItems();



        //$logger->info("Initiating print job to printer: {$printerIpAddress}");

        try {

            $connector = new NetworkPrintConnector($mainPrinter->getIpAdress(), 9100);
            //$connector = new WindowsPrintConnector('TM-T20II');
            $printer = new PrinterLib($connector);
            $printer->pulse(0, 120, 240);

            // === Business Header ===
            $printer->setJustification(PrinterLib::JUSTIFY_CENTER);
            $printer->setTextSize(2, 2);
            $printer->text("" . $storeName . "\n");
            $printer->setTextSize(1, 1);
            if ($storeAddress && $storeAddress !== '') $printer->text("{$storeAddress}\n");
            if ($storePhone && $storePhone !== '') $printer->text("Tel: {$storePhone}\n");
            if ($storeEmail && $storeEmail !== '') $printer->text("E-mail: {$storeEmail}\n");
            $printer->text("--------------------------------\n");

            // === Order Info ===
            $printer->setTextSize(2, 2);
            $printer->text("Order #: {$order->getId()}\n");
            $printer->setTextSize(1, 1);
            $printer->text(date('d M Y H:i') . "\n");
            $printer->text("--------------------------------\n");

            // Dine-in or Takeaway
            $printer->setEmphasis(true);
            $printer->text("*** " . strtoupper($order->getType()) . " ***\n");
            $printer->setEmphasis(false);
            $printer->text("--------------------------------\n");

            // === Item List ===
            $printer->setJustification(PrinterLib::JUSTIFY_LEFT);
            $printer->text("QT  ARTICLES                MONTANT\n");
            $printer->text("--------------------------------\n");

            $total = 0;
            foreach ($orderItems as $item) {
                $product = $item->getProduct();
                $name = strtoupper($product->getName());
                $qty = $item->getQuantity();
                $price = $item->getProduct()->getPrice();
                $lineTotal = $item->getPrice();
                $total += $lineTotal;

                // Product Name and Price
                $printer->setEmphasis(true);
                $printer->text(sprintf("%-2s %-22s %6.2f " . $currency . "\n", $qty, $name, $lineTotal));
                $printer->setEmphasis(false);

                // Options
                if ($item->getOptions()) {
                    foreach ($item->getOptions() as $opt) {
                        $printer->text("   • " . ucfirst($opt['name']) . ": " . ucfirst($opt['value']) . "\n");
                    }
                }
            }

            // === Summary ===
            $printer->text("--------------------------------\n");
            $printer->setEmphasis(true);
            $printer->text(sprintf("%-26s %6.2f " . $currency . "\n", "Sous Total", $total));
            $printer->text(sprintf("%-26s %6.2f " . $currency . "\n", "Réduction", 0.00));
            $printer->setEmphasis(false);
            $printer->text("--------------------------------\n");
            $printer->setEmphasis(true);
            $printer->text(sprintf("%-26s %6.2f " . $currency . "\n", "Montant Total", $total));
            $printer->setEmphasis(false);
            $printer->text("--------------------------------\n");

            // Taxes Breakdown (Example 10% VAT)
            $printer->text(sprintf("%-26s %6.2f " . $currency . "\n", "À Régler TTC", $total));
            $printer->text("--------------------------------\n");

            // === Footer ===
            $printer->setJustification(PrinterLib::JUSTIFY_CENTER);
            $printer->text("Bon Appétit chez " . $storeName . "\n");
            $printer->setEmphasis(true);
            $printer->text("*** A REGLER EN CAISSE ***\n");
            $printer->setEmphasis(false);

            // === QR Code (Example: Payment Link or Digital Receipt) ===
            $printer->feed(2);
            $printer->qrCode($order->getId(), PrinterLib::QR_ECLEVEL_L, 8);

            $printer->feed(2);

            // Transaction Details
            $printer->setJustification(PrinterLib::JUSTIFY_CENTER);
            $printer->text("Opération : VENTE\n");
            $printer->text("Borne : BORNE-{$order->getTerminal()?->getId()}\n");
            $printer->text("C. Paiement : Cash\n");
            $printer->text("Ticket : BORNE-{$order->getTerminal()?->getId()} - {$order->getId()}\n");
            $printer->text("Nombre d'impressions : 1\n");
            $printer->text(date('d M Y H:i') . "\n");

            $printer->cut();
        } catch (\Exception $e) {
            //$output->writeln("Print job failed: {$e->getMessage()}");
        } finally {
            if (isset($printer)) {
                $printer->close();
            }
        }
        return Command::SUCCESS;
    }
}
