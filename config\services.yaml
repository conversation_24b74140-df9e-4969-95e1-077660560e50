# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices.html#use-parameters-for-application-configuration
parameters:

services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: "../src/"
        exclude:
            - "../src/DependencyInjection/"
            - "../src/Entity/"
            - "../src/Kernel.php"

    # add more service definitions when explicit configuration is needed
    # please note that last definitions always *replace* previous ones
    App\Listener\AttachDataOnSuccessListener:
        tags:
            - {
                  name: kernel.event_listener,
                  event: lexik_jwt_authentication.on_authentication_success,
                  method: onAuthenticationSuccess,
              }

    App\Services\Setting\SettingService:
        arguments:
            $projectDir: '%kernel.project_dir%'

    App\Command\LocalIsLiveCommand:
        arguments:
            $globalServerUrl: '%env(GLOBAL_SERVER_URL)%'
            $globalApiKey: '%env(GLOBAL_API_KEY)%'
    #activat this listener in the config/services.yaml file whene swithcing to the production version of the app
    kernel.listener.exception_listener:
        class: App\Listener\ExceptionListener
        tags:
            - {
                  name: kernel.event_listener,
                  event: kernel.exception,
                  method: onKernelException,
              }
        calls:
            - [setLogger, ["@logger"]]

    app.jttp_handler:
        class: App\Handlers\JttpHandler
        arguments:
            - "@logger"

    app.view_handler:
        parent: fos_rest.view_handler.default
        autowire: false
        autoconfigure: false
        public: false
        calls:
            - [
                  "registerHandler",
                  ["json", ["@app.jttp_handler", "createResponse"]],
              ]
