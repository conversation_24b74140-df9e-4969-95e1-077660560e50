<?php

namespace App\Entity;

use App\Repository\ProductsRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: ProductsRepository::class)]
class Products
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    private ?string $name = null;

    #[ORM\Column(type: Types::TEXT)]
    private ?string $description = null;

    #[ORM\Column(length: 255)]
    private ?string $image = null;

    #[ORM\ManyToOne(inversedBy: 'products')]
    #[ORM\JoinColumn(nullable: true, onDelete: 'SET NULL')]
    private ?Category $category = null;

    #[ORM\ManyToOne(inversedBy: 'products')]
    private ?Category $subCategory = null;

    #[ORM\Column]
    private ?bool $isCustomizable = null;

    #[ORM\Column]
    private ?bool $isFeatured = null;

    #[ORM\Column]
    private ?bool $isAddon = null;

    #[ORM\Column]
    private ?bool $isPack = null;

    #[ORM\ManyToOne(targetEntity: self::class, inversedBy: 'inProduct')]
    private ?self $mainProduct = null;

    #[ORM\OneToMany(mappedBy: 'mainProduct', targetEntity: self::class)]
    private Collection $inProduct;

    #[ORM\Column]
    private ?float $price = null;

    #[ORM\OneToMany(mappedBy: 'product', targetEntity: Options::class, orphanRemoval: true)]
    private Collection $options;

    #[ORM\ManyToMany(targetEntity: Tags::class, inversedBy: 'product')]
    private Collection $tags;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $createdAt = null;

    #[ORM\Column(length: 255)]
    private ?string $createdBy = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $modifiedAt = null;

    #[ORM\Column(length: 255)]
    private ?string $modifedBy = null;

    #[ORM\Column]
    private ?bool $isStockable = null;

    #[ORM\Column(nullable: true)]
    private ?int $stock = null;

    #[ORM\Column(nullable: true)]
    private ?int $stockThreshold = null;

    #[ORM\Column]
    private ?float $cost = null;

    #[ORM\Column]
    private ?float $tax = null;

    #[ORM\Column(length: 255)]
    private ?string $status = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $unit = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $unitValue = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $SUK = null;

    #[ORM\Column(nullable: true)]
    private ?float $discount = null;

    #[ORM\Column(nullable: true)]
    private ?int $preparationTime = null;

    #[ORM\Column(nullable: true)]
    private ?array $ingredients = null;

    #[ORM\Column(nullable: true)]
    private ?array $attributes = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $externalId = null;

    /**
     * @var Collection<int, self>
     */
    #[ORM\ManyToMany(targetEntity: self::class, inversedBy: 'subProductIn')]
    private Collection $subProducts;

    /**
     * @var Collection<int, self>
     */
    #[ORM\ManyToMany(targetEntity: self::class, mappedBy: 'subProducts')]
    private Collection $subProductIn;

    #[ORM\Column(type: 'boolean', nullable: true)]
    private ?bool $isDeleted = null;

    public function __construct()
    {
        $this->inProduct = new ArrayCollection();
        $this->options = new ArrayCollection();
        $this->tags = new ArrayCollection();
        $this->createdAt = new \DateTime();
        $this->modifiedAt = new \DateTime();
        $this->externalId = uniqid();
        $this->subProducts = new ArrayCollection();
        $this->subProductIn = new ArrayCollection();
        $this->isDeleted = false;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(string $description): static
    {
        $this->description = $description;

        return $this;
    }

    public function getImage(): ?string
    {
        return $this->image;
    }

    public function setImage(string $image): static
    {
        $this->image = $image;

        return $this;
    }

    public function getCategory(): ?Category
    {
        return $this->category;
    }

    public function setCategory(?Category $category): static
    {
        $this->category = $category;

        return $this;
    }

    public function getSubCategory(): ?Category
    {
        return $this->subCategory;
    }

    public function setSubCategory(?Category $subCategory): static
    {
        $this->subCategory = $subCategory;

        return $this;
    }

    public function isIsCustomizable(): ?bool
    {
        return $this->isCustomizable;
    }

    public function setIsCustomizable(bool $isCustomizable): static
    {
        $this->isCustomizable = $isCustomizable;

        return $this;
    }

    public function isIsFeatured(): ?bool
    {
        return $this->isFeatured;
    }

    public function setIsFeatured(bool $isFeatured): static
    {
        $this->isFeatured = $isFeatured;

        return $this;
    }

    public function isIsAddon(): ?bool
    {
        return $this->isAddon;
    }

    public function setIsAddon(bool $isAddon): static
    {
        $this->isAddon = $isAddon;

        return $this;
    }

    public function isIsPack(): ?bool
    {
        return $this->isPack;
    }

    public function setIsPack(bool $isPack): static
    {
        $this->isPack = $isPack;

        return $this;
    }

    public function getMainProduct(): ?self
    {
        return $this->mainProduct;
    }

    public function setMainProduct(?self $mainProduct): static
    {
        $this->mainProduct = $mainProduct;

        return $this;
    }

    /**
     * @return Collection<int, self>
     */
    public function getInProduct(): Collection
    {
        return $this->inProduct;
    }

    public function addInProduct(self $inProduct): static
    {
        if (!$this->inProduct->contains($inProduct)) {
            $this->inProduct->add($inProduct);
            $inProduct->setMainProduct($this);
        }

        return $this;
    }

    public function removeInProduct(self $inProduct): static
    {
        if ($this->inProduct->removeElement($inProduct)) {
            // set the owning side to null (unless already changed)
            if ($inProduct->getMainProduct() === $this) {
                $inProduct->setMainProduct(null);
            }
        }

        return $this;
    }

    public function getPrice(): ?float
    {
        return $this->price;
    }

    public function setPrice(float $price): static
    {
        $this->price = $price;

        return $this;
    }

    /**
     * @return Collection<int, Options>
     */
    public function getOptions(): Collection
    {
        return $this->options;
    }

    public function addOption(Options $option): static
    {
        if (!$this->options->contains($option)) {
            $this->options->add($option);
            $option->setProduct($this);
        }

        return $this;
    }

    public function removeOption(Options $option): static
    {
        if ($this->options->removeElement($option)) {
            // set the owning side to null (unless already changed)
            if ($option->getProduct() === $this) {
                $option->setProduct(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Tags>
     */
    public function getTags(): Collection
    {
        return $this->tags;
    }

    public function addTag(Tags $tag): static
    {
        if (!$this->tags->contains($tag)) {
            $this->tags->add($tag);
        }

        return $this;
    }

    public function removeTag(Tags $tag): static
    {
        $this->tags->removeElement($tag);

        return $this;
    }

    public function getCreatedAt(): ?\DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeInterface $createdAt): static
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    public function getCreatedBy(): ?string
    {
        return $this->createdBy;
    }

    public function setCreatedBy(string $createdBy): static
    {
        $this->createdBy = $createdBy;

        return $this;
    }

    public function getModifiedAt(): ?\DateTimeInterface
    {
        return $this->modifiedAt;
    }

    public function setModifiedAt(\DateTimeInterface $modifiedAt): static
    {
        $this->modifiedAt = $modifiedAt;

        return $this;
    }

    public function getModifedBy(): ?string
    {
        return $this->modifedBy;
    }

    public function setModifedBy(string $modifedBy): static
    {
        $this->modifedBy = $modifedBy;

        return $this;
    }

    public function toArray($compact = false): array
    {
        if ($compact) {
            return [
                'id' => $this->getId(),
                'name' => $this->getName(),
                'description' => $this->getDescription(),
                'image' => $this->getImage(),
                'category' => $this->getCategory() ? $this->getCategory()->toArray(false) : 'none',
                'subCategory' => $this->getSubCategory() ? $this->getSubCategory()->getName() : null,
                'price' => $this->getPrice(),
            ];
        }
        return [
            'id' => $this->getId(),
            'name' => $this->getName(),
            'description' => $this->getDescription(),
            'image' => $this->getImage(),
            'category' => $this->getCategory() ? $this->getCategory()->toArray(false) : 'none',
            'subCategory' => $this->getSubCategory() ? $this->getSubCategory()->toArray(false) : 'none',
            'isCustomizable' => $this->isIsCustomizable(),
            'isFeatured' => $this->isIsFeatured(),
            'isAddon' => $this->isIsAddon(),
            'isPack' => $this->isIsPack(),
            'mainProductId' => $this->isIsPack() ? $this->getMainProduct()->getId() : null,
            'price' => $this->getPrice(),
            'options' => $this->getOptions()->map(fn(Options $option) => $option->toArray()),
            'tags' => $this->getTags()->map(fn(Tags $tag) => $tag->toArray()),

            'isStockable' => $this->isStockable(),
            'stockThreshold' => $this->getStockThreshold(),
            'cost' => $this->getCost(),
            'taxRate' => $this->getTax(),
            'preparationTime' => $this->getPreparationTime(),
            'suk' => $this->getSUK(),
            'stock' => $this->getStock(),
            'unit' => $this->getUnit(),
            'unitValue' => $this->getUnitValue(),
            'ingredients' => $this->getIngredients(),
            'attributes' => $this->getAttributes(),
            'discount' => $this->getDiscount(),
            'status' => $this->getStatus(),
            'subProducts' => $this->getSubProducts()->map(fn(self $subProduct) => [...$subProduct->toArray(true), 'isMainProduct' => $subProduct->getId() === $this->getMainProduct()->getId()]),
        ];
    }

    public function isStockable(): ?bool
    {
        return $this->isStockable;
    }

    public function setStockable(bool $isStockable): static
    {
        $this->isStockable = $isStockable;

        return $this;
    }

    public function getStock(): ?int
    {
        return $this->stock;
    }

    public function setStock(?int $stock): static
    {
        $this->stock = $stock;

        return $this;
    }

    public function getStockThreshold(): ?int
    {
        return $this->stockThreshold;
    }

    public function setStockThreshold(?int $stockThreshold): static
    {
        $this->stockThreshold = $stockThreshold;

        return $this;
    }

    public function getCost(): ?float
    {
        return $this->cost;
    }

    public function setCost(float $cost): static
    {
        $this->cost = $cost;

        return $this;
    }

    public function getTax(): ?float
    {
        return $this->tax;
    }

    public function setTax(float $tax): static
    {
        $this->tax = $tax;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): static
    {
        $this->status = $status;

        return $this;
    }

    public function getUnit(): ?string
    {
        return $this->unit;
    }

    public function setUnit(?string $unit): static
    {
        $this->unit = $unit;

        return $this;
    }

    public function getUnitValue(): ?string
    {
        return $this->unitValue;
    }

    public function setUnitValue(?string $unitValue): static
    {
        $this->unitValue = $unitValue;

        return $this;
    }

    public function getSUK(): ?string
    {
        return $this->SUK;
    }

    public function setSUK(?string $SUK): static
    {
        $this->SUK = $SUK;

        return $this;
    }

    public function getDiscount(): ?float
    {
        return $this->discount;
    }

    public function setDiscount(?float $discount): static
    {
        $this->discount = $discount;

        return $this;
    }

    public function getPreparationTime(): ?int
    {
        return $this->preparationTime;
    }

    public function setPreparationTime(?int $preparationTime): static
    {
        $this->preparationTime = $preparationTime;

        return $this;
    }

    public function getIngredients(): ?array
    {
        return $this->ingredients;
    }

    public function setIngredients(?array $ingredients): static
    {
        $this->ingredients = $ingredients;

        return $this;
    }

    public function getAttributes(): ?array
    {
        return $this->attributes;
    }

    public function setAttributes(?array $attributes): static
    {
        $this->attributes = $attributes;

        return $this;
    }

    public function getExternalId(): ?string
    {
        return $this->externalId;
    }

    public function setExternalId(string $externalId): static
    {
        $this->externalId = $externalId;

        return $this;
    }

    /**
     * @return Collection<int, self>
     */
    public function getSubProducts(): Collection
    {
        return $this->subProducts;
    }

    public function addSubProduct(self $subProduct): static
    {
        if (!$this->subProducts->contains($subProduct)) {
            $this->subProducts->add($subProduct);
        }

        return $this;
    }

    public function removeSubProduct(self $subProduct): static
    {
        $this->subProducts->removeElement($subProduct);

        return $this;
    }

    /**
     * @return Collection<int, self>
     */
    public function getSubProductIn(): Collection
    {
        return $this->subProductIn;
    }

    public function addSubProductIn(self $subProductIn): static
    {
        if (!$this->subProductIn->contains($subProductIn)) {
            $this->subProductIn->add($subProductIn);
            $subProductIn->addSubProduct($this);
        }

        return $this;
    }

    public function removeSubProductIn(self $subProductIn): static
    {
        if ($this->subProductIn->removeElement($subProductIn)) {
            $subProductIn->removeSubProduct($this);
        }

        return $this;
    }

    public function isDeleted(): ?bool
    {
        return $this->isDeleted;
    }

    public function setDeleted(bool $isDeleted): static
    {
        $this->isDeleted = $isDeleted;

        return $this;
    }
}
