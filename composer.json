{"type": "project", "license": "proprietary", "minimum-stability": "stable", "prefer-stable": true, "require": {"php": ">=8.0.2", "ext-ctype": "*", "ext-iconv": "*", "cboden/ratchet": "^0.4.4", "doctrine/dbal": "^3", "doctrine/doctrine-bundle": "^2.6", "doctrine/doctrine-migrations-bundle": "^3.0", "doctrine/orm": "^3.3", "friendsofsymfony/rest-bundle": "^3.3", "gesdinet/jwt-refresh-token-bundle": "^1.0", "intervention/image": "^3.8", "jms/serializer-bundle": "^4.0", "lexik/jwt-authentication-bundle": "^2.15", "mike42/escpos-php": "^4.0", "scienta/doctrine-json-functions": "^5.4", "sensio/framework-extra-bundle": "^6.2", "symfony-bundles/json-request-bundle": "^4.1", "symfony/console": "6.4.*", "symfony/dotenv": "6.4.*", "symfony/flex": "^2", "symfony/framework-bundle": "6.4.*", "symfony/http-client": "6.4", "symfony/mime": "6.4", "symfony/monolog-bundle": "^3.7", "symfony/process": "6.4", "symfony/proxy-manager-bridge": "6.4.*", "symfony/runtime": "6.4.*", "symfony/security-bundle": "6.4.*", "symfony/validator": "6.4.*", "symfony/yaml": "6.4.*", "textalk/websocket": "^1.5"}, "config": {"allow-plugins": {"composer/package-versions-deprecated": true, "symfony/flex": true, "symfony/runtime": true}, "optimize-autoloader": true, "preferred-install": {"*": "dist"}, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": true, "require": "6.4", "docker": true}}, "require-dev": {"doctrine/doctrine-fixtures-bundle": "^3.5", "symfony/maker-bundle": "^1.38", "zenstruck/foundry": "^1.36"}}