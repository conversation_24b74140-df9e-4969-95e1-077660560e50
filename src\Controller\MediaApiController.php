<?php

namespace App\Controller;

use App\Services\Media\MediaServiceInterface;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations as Rest;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;

/**
 * @Rest\Route("/media" , name="media_")
 */
class MediaApiController extends AbstractFOSRestController
{

    private $mediaService;
    public function __construct(MediaServiceInterface $mediaService)
    {
        $this->mediaService = $mediaService;
    }

    /**
     * @Rest\Get("/locales/{filename}" , name="getLanguageFile")
     */
    public function serveProductFile(string $filename)
    {
        if ($filename === 'ar') {
            $filename = 'ar_TN.json';
        }
        if ($filename === 'en') {
            $filename = 'en_US.json';
        }
        if ($filename === 'fr') {
            $filename = 'fr_FR.json';
        }
        $filePath = $this->getParameter('kernel.project_dir') . '/public/locales/' . $filename;

        if (!file_exists($filePath)) {
            throw $this->createNotFoundException("File not found.");
        }

        $response = new BinaryFileResponse($filePath);
        $response->setContentDisposition(ResponseHeaderBag::DISPOSITION_INLINE, $filename);

        return $response;
    }

    /**
     * @Rest\Post("/" , name="create")
     */
    public function postMedia(Request $request)
    {
        $res = $this->mediaService->addMedia($request);
        $view = $this->view($res["data"], $res['status'], []);
        return $this->handleView($view);
    }

    /**
     * @Rest\Get("/type/{type}" , name="get_by_type" )
     */
    public function getMediaByType($type)
    {
        $res = $this->mediaService->getMediaByType($type);
        $view = $this->view($res["data"], $res['status'], []);
        return $this->handleView($view);
    }

    /**
     * @Rest\Get("/" , name="get_all")
     */
    public function getAllMedia()
    {
        $res = $this->mediaService->getAllMedia();
        $view = $this->view($res["data"], $res['status'], []);
        return $this->handleView($view);
    }


    /**
     * @Rest\Get("/{id}" , name="by_id" , requirements={"id"="\d+"})
     */
    public function getMediaById($id)
    {
        $res = $this->mediaService->getMediaById($id);
        $view = $this->view($res["data"], $res['status'], []);
        return $this->handleView($view);
    }

    /**
     * @Rest\Post("/delete/{id}" , name="delete" , requirements={"id"="\d+"})
     */
    public function delete($id)
    {
        $res = $this->mediaService->deleteMedia($id);
        $view = $this->view($res["data"], $res['status'], []);
        return $this->handleView($view);
    }

    /**
     * @Rest\Post("/{id}" , name="update" , requirements={"id"="\d+"})
     */
    public function update(Request $request, $id)
    {
        $res = $this->mediaService->updateMedia($request, $id);
        $view = $this->view($res["data"], $res['status'], []);
        return $this->handleView($view);
    }

    /**
     * @Rest\Post("/doAction" , name="do_action")
     */
    public function doAction(Request $request)
    {
        $res = $this->mediaService->doAction($request);
        $view = $this->view($res["data"], $res['status'], []);
        return $this->handleView($view);
    }
}
