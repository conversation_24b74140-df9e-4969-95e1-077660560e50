<?php

namespace App\Services\OptionValues;

use App\Entity\OptionValues;
use Doctrine\ORM\EntityManagerInterface;
use App\Services\OptionValues\OptionValuesServiceInterface;
use App\Utils\Uploader;
use DateTime;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Security\Core\Security;
use Symfony\Component\HttpFoundation\Response;

class OptionValuesService implements OptionValuesServiceInterface
{

    private EntityManagerInterface $em;
    private Security $security;

    public function __construct(EntityManagerInterface $entityManager, Security $security)
    {
        $this->em = $entityManager;
        $this->security = $security;
    }

    public function addOptionValue(Request $request): array
    {
        if ($request->get('price') === null || floatval($request->get('price')) < 0) {
            return [
                'status' => Response::HTTP_BAD_REQUEST,
                'data' => 'price cannot be negative'
            ];
        }
        $file = $request->files->get('image');
        if (!$file) {
            return [
                'status' => Response::HTTP_BAD_REQUEST,
                'data' => "Image Is requierd"
            ];
        }
        $optionValue = new OptionValues();
        $optionValue->setName($request->get('name'));
        $optionValue->setPrice(floatval($request->get('price')));
        $imageLink = Uploader::uploadImageToWebp($file, 'optionValue');
        $optionValue->setImage($imageLink);
        $this->em->persist($optionValue);
        $this->em->flush();
        return [
            'status' => Response::HTTP_OK,
            'data' => $optionValue->toArray()
        ];
    }

    public function deleteOptionValue(int $id): array
    {
        $optionValue = $this->em->getRepository(OptionValues::class)->find($id);
        if (!$optionValue) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'data' => 'option value not found'
            ];
        }
        $this->em->remove($optionValue);
        $this->em->flush();
        return [
            'status' => Response::HTTP_OK,
            'data' => ['deleted successfully']
        ];
    }

    public function updateOptionValue(Request $request, int $id): array
    {
        $optionValue = $this->em->getRepository(OptionValues::class)->find($id);
        if (!$optionValue) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'data' => 'option value not found'
            ];
        }
        $optionValue->setName($request->get('name'));
        $optionValue->setPrice(floatval($request->get('price')));
        $this->em->persist($optionValue);
        $this->em->flush();
        return [
            'status' => Response::HTTP_OK,
            'data' => $optionValue->toArray()
        ];
    }

    public function updateOptionValueImage(Request $request, int $id): array
    {
        $optionValue = $this->em->getRepository(OptionValues::class)->find($id);
        if (!$optionValue) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'data' => 'option value not found'
            ];
        }
        $file = $request->files->get('image');
        if (!$file) {
            return [
                'status' => Response::HTTP_BAD_REQUEST,
                'data' => "Image Is requierd"
            ];
        }
        $oldImage = $optionValue->getImage();
        $imageLink = Uploader::uploadImageToWebp($file, 'optionValue');
        $optionValue->setImage($imageLink);
        if ($oldImage) {
            Uploader::deleteFile($oldImage);
        }
        $this->em->persist($optionValue);
        $this->em->flush();
        return [
            'status' => Response::HTTP_OK,
            'data' => $optionValue->toArray()
        ];
    }

    public function getOptionValueById(int $id): array
    {
        $optionValue = $this->em->getRepository(OptionValues::class)->find($id);
        return [
            'status' => Response::HTTP_OK,
            'data' => $optionValue->toArray()
        ];
    }

    public function getOptionValues(): array
    {
        $optionValues = $this->em->getRepository(OptionValues::class)->findAll();
        $result = [];
        foreach ($optionValues as $optionValue) {
            $result[] = $optionValue->toArray();
        }
        return [
            'status' => Response::HTTP_OK,
            'data' => $result
        ];
    }

    public function doAction(Request $request): array
    {
        $action = $request->get('action');
        if (!$action) {
            return [
                'status' => Response::HTTP_BAD_REQUEST,
                'data' => 'action is required'
            ];
        }
        switch ($action) {
            case 'changeOptionValueImage':
                return $this->updateOptionValueImage($request, $request->get('id'));
            default:
                return [
                    'status' => Response::HTTP_BAD_REQUEST,
                    'data' => 'invalid action'
                ];
        }
    }
}
